"""
基础工具类 - 所有工具的基类
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, List
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class ToolResult:
    """工具执行结果"""
    success: bool
    data: Optional[Any] = None
    error: Optional[str] = None
    message: Optional[str] = None
    execution_time: Optional[float] = None
    metadata: Optional[Dict] = None

    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'success': self.success,
            'data': self.data,
            'error': self.error,
            'message': self.message,
            'execution_time': self.execution_time,
            'metadata': self.metadata or {},
            'timestamp': datetime.now().isoformat()
        }

    def get(self, key: str, default: Any = None) -> Any:
        """
        提供类似字典的get方法，用于兼容性
        修复 'ToolResult' object has no attribute 'get' 错误
        """
        if hasattr(self, key):
            return getattr(self, key, default)
        else:
            return default

    def to_markdown(self) -> str:
        """转换为Markdown格式"""
        status = "✅ 成功" if self.success else "❌ 失败"

        md = f"## {status}\n\n"

        if self.message:
            md += f"**消息**: {self.message}\n\n"

        if self.error:
            md += f"**错误**: {self.error}\n\n"

        if self.data:
            md += f"**结果数据**:\n```json\n{self.data}\n```\n\n"

        if self.execution_time:
            md += f"**执行时间**: {self.execution_time:.2f}秒\n\n"

        return md


class BaseTool(ABC):
    """
    基础工具类

    所有工具都应该继承这个类并实现相应的方法
    """

    def __init__(self):
        self.name = self.__class__.__name__
        self.logger = logging.getLogger(f"tools.{self.name}")

    @abstractmethod
    async def execute(self, *args, **kwargs) -> ToolResult:
        """
        执行工具操作

        Returns:
            ToolResult: 执行结果
        """
        pass

    def get_capabilities(self) -> List[str]:
        """
        获取工具能力列表

        Returns:
            List[str]: 能力列表
        """
        return []

    def get_description(self) -> str:
        """
        获取工具描述

        Returns:
            str: 工具描述
        """
        return f"{self.name} - 基础工具"

    def validate_input(self, *args, **kwargs) -> bool:
        """
        验证输入参数

        Returns:
            bool: 验证结果
        """
        return True

    async def cleanup(self):
        """清理资源"""
        pass

    def log_operation(self, operation: str, details: Dict = None):
        """记录操作日志"""
        self.logger.info(f"执行操作: {operation}", extra={'details': details or {}})

    def log_error(self, error: Exception, context: str = ""):
        """记录错误日志"""
        self.logger.error(f"操作失败 {context}: {error}", exc_info=True)


class ToolRegistry:
    """工具注册表"""

    def __init__(self):
        self._tools: Dict[str, BaseTool] = {}

    def register(self, tool: BaseTool):
        """注册工具"""
        self._tools[tool.name] = tool
        logger.info(f"注册工具: {tool.name}")

    def get_tool(self, name: str) -> Optional[BaseTool]:
        """获取工具"""
        return self._tools.get(name)

    def list_tools(self) -> List[str]:
        """列出所有工具"""
        return list(self._tools.keys())

    def get_all_capabilities(self) -> Dict[str, List[str]]:
        """获取所有工具的能力"""
        return {
            name: tool.get_capabilities()
            for name, tool in self._tools.items()
        }


# 全局工具注册表
tool_registry = ToolRegistry()
