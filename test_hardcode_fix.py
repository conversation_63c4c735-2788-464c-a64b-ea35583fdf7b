#!/usr/bin/env python3
"""
测试硬编码修复效果 - 验证系统是否不再硬编码错误类型
"""

import sys
import os
import asyncio
import traceback

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_hardcode_fix():
    """测试硬编码修复效果"""
    print("🔧 硬编码修复效果测试")
    print("=" * 50)

    try:
        from bot_agent.tools.intelligent_tool_coordinator import global_tool_coordinator

        # 测试1：网络超时错误分析
        print("\n🌐 测试1：网络超时错误分析...")

        job_info = {
            'name': 'lint',
            'status': 'failed',
            'id': 927
        }

        # 模拟网络超时的作业日志
        network_timeout_log = """
[32;1m$ pip install black==23.3.0 flake8==6.0.0[0;m
Collecting black==23.3.0
  Downloading black-23.3.0-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.7 MB)
     âââââââââââââââââââââââââ¸                1.0/1.7 MB 36.0 kB/s eta 0:00:18
ERROR: Exception:
socket.timeout: The read operation timed out
pip._vendor.urllib3.exceptions.ReadTimeoutError: HTTPSConnectionPool(host='files.pythonhosted.org', port=443): Read timed out.
[0K[31;1mERROR: Job failed: exit code 1
"""

        try:
            error_analysis_result = await global_tool_coordinator.async_coordinator.analyze_job_errors(network_timeout_log, job_info)
            print(f"✅ 网络超时错误分析: {'成功' if error_analysis_result.success else '失败'}")

            if error_analysis_result.success and error_analysis_result.data:
                all_errors = error_analysis_result.data.get('all_errors', [])
                print(f"   发现错误数: {len(all_errors)}")

                # 检查是否错误地识别为flake8配置错误
                flake8_errors = 0
                network_errors = 0

                for error in all_errors:
                    # 正确提取错误内容
                    if isinstance(error, dict):
                        error_content = error.get('content', str(error)).lower()
                        error_type = error.get('type', 'unknown')
                        error_category = error.get('category', 'unknown')
                    else:
                        error_content = str(error).lower()
                        error_type = 'unknown'
                        error_category = 'unknown'

                    if 'flake8' in error_content or 'extend-ignore' in error_content or error_type == 'lint_error':
                        flake8_errors += 1
                        print(f"   ❌ 错误识别为flake8问题: {error_content[:100]}...")
                    elif 'timeout' in error_content or 'network' in error_content or 'connection' in error_content or error_type == 'network_error':
                        network_errors += 1
                        print(f"   ✅ 正确识别为网络问题: {error_content[:100]}...")
                    else:
                        print(f"   ⚠️ 其他错误[{error_type}]: {error_content[:100]}...")

                print(f"📊 错误分类结果:")
                print(f"  - flake8错误: {flake8_errors} 个")
                print(f"  - 网络错误: {network_errors} 个")

                if flake8_errors == 0:
                    print("✅ 网络超时错误没有被误判为flake8配置错误")
                    network_test_passed = True
                else:
                    print("❌ 网络超时错误被误判为flake8配置错误")
                    network_test_passed = False

        except Exception as e:
            print(f"❌ 网络超时错误分析失败: {e}")
            network_test_passed = False

        # 测试2：真正的flake8配置错误分析
        print("\n🔧 测试2：真正的flake8配置错误分析...")

        # 模拟真正的flake8配置错误日志
        flake8_config_log = """
[32;1m$ flake8 --config .flake8[0;m
Traceback (most recent call last):
  File "/usr/local/bin/flake8", line 8, in <module>
    sys.exit(main())
ValueError: Error code 'Ignore' supplied to 'extend-ignore' option does not match '^[A-Z]{1,3}[0-9]{0,3}$'
[0K[31;1mERROR: Job failed: exit code 1
"""

        try:
            error_analysis_result = await global_tool_coordinator.async_coordinator.analyze_job_errors(flake8_config_log, job_info)
            print(f"✅ flake8配置错误分析: {'成功' if error_analysis_result.success else '失败'}")

            if error_analysis_result.success and error_analysis_result.data:
                all_errors = error_analysis_result.data.get('all_errors', [])
                print(f"   发现错误数: {len(all_errors)}")

                # 检查是否正确识别为flake8配置错误
                flake8_errors = 0
                network_errors = 0

                for error in all_errors:
                    error_content = str(error).lower()
                    if 'flake8' in error_content or 'extend-ignore' in error_content:
                        flake8_errors += 1
                        print(f"   ✅ 正确识别为flake8问题: {str(error)[:100]}...")
                    elif 'timeout' in error_content or 'network' in error_content:
                        network_errors += 1
                        print(f"   ❌ 错误识别为网络问题: {str(error)[:100]}...")
                    else:
                        print(f"   ⚠️ 其他错误: {str(error)[:100]}...")

                print(f"📊 错误分类结果:")
                print(f"  - flake8错误: {flake8_errors} 个")
                print(f"  - 网络错误: {network_errors} 个")

                if flake8_errors > 0 and network_errors == 0:
                    print("✅ flake8配置错误被正确识别")
                    flake8_test_passed = True
                else:
                    print("❌ flake8配置错误识别有问题")
                    flake8_test_passed = False

        except Exception as e:
            print(f"❌ flake8配置错误分析失败: {e}")
            flake8_test_passed = False

        # 测试3：验证AI修复方案生成
        print("\n🤖 测试3：验证AI修复方案生成...")

        try:
            # 使用网络超时错误测试AI修复方案
            if error_analysis_result.success and error_analysis_result.data:
                all_errors = error_analysis_result.data.get('all_errors', [])

                if all_errors:
                    project_path = "E:\\aider-git-repos\\ai-proxy"
                    ai_fix_result = await global_tool_coordinator.async_coordinator._ai_generate_fix_plan(all_errors, project_path)

                    if ai_fix_result.success:
                        print("✅ AI修复方案生成成功")

                        fix_plan = []
                        if ai_fix_result.data and isinstance(ai_fix_result.data, dict):
                            fix_plan = ai_fix_result.data.get('fix_plan', [])

                        if fix_plan:
                            print(f"🤖 AI生成了 {len(fix_plan)} 个修复步骤")

                            # 分析修复方案是否合理
                            for i, step in enumerate(fix_plan[:3]):  # 只显示前3个步骤
                                step_description = step.get('description', '') if isinstance(step, dict) else str(step)
                                step_command = step.get('command', '') if isinstance(step, dict) else ''

                                print(f"   步骤{i+1}: {step_description}")
                                if step_command:
                                    print(f"     命令: {step_command}")

                            ai_test_passed = True
                        else:
                            print("❌ AI修复方案为空")
                            ai_test_passed = False
                    else:
                        print("❌ AI修复方案生成失败")
                        ai_test_passed = False
                else:
                    print("❌ 没有错误可供修复")
                    ai_test_passed = False

        except Exception as e:
            print(f"❌ AI修复方案生成失败: {e}")
            ai_test_passed = False

        # 总结测试结果
        print("\n🎯 测试结果总结:")
        print(f"  - 网络超时错误识别: {'✅ 通过' if network_test_passed else '❌ 失败'}")
        print(f"  - flake8配置错误识别: {'✅ 通过' if flake8_test_passed else '❌ 失败'}")
        print(f"  - AI修复方案生成: {'✅ 通过' if ai_test_passed else '❌ 失败'}")

        all_tests_passed = network_test_passed and flake8_test_passed and ai_test_passed

        if all_tests_passed:
            print("\n🎉 所有测试通过！硬编码问题已修复")
            print("\n💡 修复效果:")
            print("1. ✅ 系统不再硬编码错误类型")
            print("2. ✅ 网络超时错误不会被误判为flake8配置错误")
            print("3. ✅ AI能够正确分析不同类型的错误")
            print("4. ✅ AI能够生成针对性的修复方案")
            print("\n🚀 现在task_1748505049_1748505049类似的任务应该能够正确处理！")
            return True
        else:
            print("\n❌ 部分测试失败！需要进一步修复")
            return False

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print("🔍 详细错误堆栈:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_hardcode_fix())
    sys.exit(0 if success else 1)
