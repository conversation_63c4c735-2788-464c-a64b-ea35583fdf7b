{% extends "base.html" %}

{% block title %}会话详情 - 对话分析系统{% endblock %}

{% block extra_css %}
<link href="/static/css/analysis_chain.css" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-eye text-primary"></i>
        会话详情
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="history.back()">
                <i class="fas fa-arrow-left"></i> 返回
            </button>
            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewExecutionFlow()">
                <i class="fas fa-project-diagram"></i> 查看执行链路
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportSession()">
                <i class="fas fa-download"></i> 导出
            </button>
        </div>
    </div>
</div>

<!-- 会话基本信息 -->
<div class="row mb-4" id="sessionInfo">
    <div class="col-12">
        <div class="loading">
            <div class="spinner"></div>
            <div>加载会话信息...</div>
        </div>
    </div>
</div>

<!-- 分析链路 -->
<div class="row mb-4">
    <div class="col-12">
        <div id="analysisChainContainer">
            <!-- 分析链路将在这里渲染 -->
        </div>
    </div>
</div>

<!-- 对话轮次 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-comments"></i>
                    对话轮次
                </h5>
            </div>
            <div class="card-body">
                <div id="conversationRounds">
                    <div class="loading">
                        <div class="spinner"></div>
                        <div>加载对话内容...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="/static/js/analysis_chain.js"></script>
<script>
let currentSession = null;
let analysisChain = null;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    const sessionId = '{{ session_id }}';
    analysisChain = new AnalysisChain('analysisChainContainer');
    loadSessionDetail(sessionId);
});

// 加载会话详情
async function loadSessionDetail(sessionId) {
    try {
        const response = await fetch(`/api/sessions/${sessionId}`);
        const data = await response.json();

        if (data.success) {
            currentSession = data.data;
            updateSessionInfo(currentSession);
            updateConversationRounds(currentSession.rounds);

            // 渲染分析链路
            if (analysisChain && currentSession.rounds.length > 0) {
                analysisChain.renderAnalysisChain(currentSession);
            }
        } else {
            showError('sessionInfo', data.error || '加载会话详情失败');
            showError('conversationRounds', '无法加载对话内容');
            showError('analysisChainContainer', '无法加载分析链路');
        }
    } catch (error) {
        console.error('加载会话详情失败:', error);
        showError('sessionInfo', '网络错误，请稍后重试');
        showError('conversationRounds', '网络错误，请稍后重试');
        showError('analysisChainContainer', '网络错误，请稍后重试');
    }
}

// 更新会话基本信息
function updateSessionInfo(session) {
    const container = document.getElementById('sessionInfo');

    const html = `
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">${session.task_title}</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-2">
                                <strong>会话ID:</strong>
                                <code>${session.session_id}</code>
                                <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('${session.session_id}')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </p>
                            <p class="mb-2"><strong>任务类型:</strong> <span class="badge bg-info">${session.task_type}</span></p>
                            <p class="mb-2"><strong>项目路径:</strong> <code>${session.project_path}</code></p>
                            <p class="mb-2"><strong>状态:</strong> ${getStatusBadge(session.status)}</p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-2"><strong>开始时间:</strong> ${formatTime(session.started_at)}</p>
                            <p class="mb-2"><strong>结束时间:</strong> ${session.ended_at ? formatTime(session.ended_at) : '进行中'}</p>
                            <p class="mb-2"><strong>总时长:</strong> ${session.total_duration ? formatDuration(session.total_duration) : '计算中'}</p>
                            <p class="mb-2"><strong>对话轮次:</strong> <span class="badge bg-primary">${session.rounds.length}</span></p>
                        </div>
                    </div>
                    ${session.metadata ? `
                        <div class="mt-3">
                            <strong>元数据:</strong>
                            <pre class="code-block mt-2">${JSON.stringify(session.metadata, null, 2)}</pre>
                        </div>
                    ` : ''}
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">统计信息</h6>
                </div>
                <div class="card-body">
                    ${generateSessionStats(session)}
                </div>
            </div>
        </div>
    `;

    container.innerHTML = html;
}

// 生成会话统计信息
function generateSessionStats(session) {
    const rounds = session.rounds;
    const successRounds = rounds.filter(r => r.status === 'success').length;
    const failedRounds = rounds.filter(r => r.status === 'failed').length;
    const totalDuration = rounds.reduce((sum, r) => sum + (r.duration || 0), 0);
    const avgDuration = rounds.length > 0 ? totalDuration / rounds.length : 0;

    // 计算Token使用
    let totalTokens = 0;
    let promptTokens = 0;
    let completionTokens = 0;

    rounds.forEach(round => {
        if (round.token_usage) {
            totalTokens += round.token_usage.total_tokens || 0;
            promptTokens += round.token_usage.prompt_tokens || 0;
            completionTokens += round.token_usage.completion_tokens || 0;
        }
    });

    return `
        <div class="mb-3">
            <div class="d-flex justify-content-between">
                <span>成功轮次:</span>
                <span class="text-success">${successRounds}</span>
            </div>
            <div class="d-flex justify-content-between">
                <span>失败轮次:</span>
                <span class="text-danger">${failedRounds}</span>
            </div>
            <div class="d-flex justify-content-between">
                <span>成功率:</span>
                <span>${rounds.length > 0 ? ((successRounds / rounds.length) * 100).toFixed(1) : 0}%</span>
            </div>
        </div>
        <hr>
        <div class="mb-3">
            <div class="d-flex justify-content-between">
                <span>总时长:</span>
                <span>${formatDuration(totalDuration)}</span>
            </div>
            <div class="d-flex justify-content-between">
                <span>平均时长:</span>
                <span>${formatDuration(avgDuration)}</span>
            </div>
        </div>
        ${totalTokens > 0 ? `
            <hr>
            <div class="mb-3">
                <div class="d-flex justify-content-between">
                    <span>总Token:</span>
                    <span>${totalTokens.toLocaleString()}</span>
                </div>
                <div class="d-flex justify-content-between">
                    <span>输入Token:</span>
                    <span>${promptTokens.toLocaleString()}</span>
                </div>
                <div class="d-flex justify-content-between">
                    <span>输出Token:</span>
                    <span>${completionTokens.toLocaleString()}</span>
                </div>
            </div>
        ` : ''}
    `;
}

// 更新对话轮次
function updateConversationRounds(rounds) {
    const container = document.getElementById('conversationRounds');

    if (rounds.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-comments fa-3x mb-3"></i>
                <div>暂无对话记录</div>
            </div>
        `;
        return;
    }

    // 按轮次编号排序，确保显示顺序正确
    const sortedRounds = rounds.sort((a, b) => a.round_number - b.round_number);

    const html = sortedRounds.map((round, index) => `
        <div class="conversation-round ${round.status}" id="round-${round.round_number}">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0">
                    <i class="fas fa-comment-dots"></i>
                    ${round.round_name.startsWith(`第${round.round_number}轮`) ? round.round_name : `第${round.round_number}轮：${round.round_name}`}
                </h6>
                <div class="d-flex align-items-center">
                    ${getStatusBadge(round.status)}
                    <span class="ms-2 text-muted small">
                        ${formatDuration(round.duration)}
                        ${round.retry_count > 0 ? `<span class="badge bg-warning ms-1">${round.retry_count}次重试</span>` : ''}
                    </span>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <small class="text-muted">
                        <i class="fas fa-clock"></i> ${formatTime(round.timestamp)}
                    </small>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        <i class="fas fa-robot"></i> ${round.model_name}
                        ${round.token_usage ? `
                            <span class="ms-2">
                                <i class="fas fa-coins"></i> ${round.token_usage.total_tokens || 0} tokens
                            </span>
                        ` : ''}
                    </small>
                </div>
            </div>

            ${round.error_message ? `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>错误:</strong> ${round.error_message}
                </div>
            ` : ''}

            <!-- 提示词分析区域 -->
            <div class="mb-4">
                <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-user"></i> 输入提示词分析
                            <button class="btn btn-sm btn-outline-light ms-2" onclick="copyToClipboard(\`${round.prompt.replace(/`/g, '\\`')}\`)">
                                <i class="fas fa-copy"></i> 复制
                            </button>
                            <button class="btn btn-sm btn-outline-light ms-1" onclick="analyzePrompt(${round.round_number})">
                                <i class="fas fa-search"></i> 分析
                            </button>
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="prompt-content" style="max-height: 400px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: 'Courier New', monospace; font-size: 13px; line-height: 1.4;">
                                    ${round.prompt ? formatPromptContent(round.prompt) : '<span class="text-muted">无内容</span>'}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="prompt-stats">
                                    <h6 class="text-muted">提示词统计</h6>
                                    <div class="small">
                                        <div class="d-flex justify-content-between">
                                            <span>字符数:</span>
                                            <span class="badge bg-info">${round.prompt ? round.prompt.length : 0}</span>
                                        </div>
                                        <div class="d-flex justify-content-between mt-1">
                                            <span>行数:</span>
                                            <span class="badge bg-info">${round.prompt ? round.prompt.split('\\n').length : 0}</span>
                                        </div>
                                        <div class="d-flex justify-content-between mt-1">
                                            <span>约Token:</span>
                                            <span class="badge bg-warning">${round.prompt ? Math.ceil(round.prompt.length / 4) : 0}</span>
                                        </div>
                                    </div>
                                    <div class="mt-3" id="prompt-analysis-${round.round_number}">
                                        <!-- 提示词分析结果将显示在这里 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI响应区域 -->
            <div class="mb-4">
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-robot"></i> AI响应分析
                            <button class="btn btn-sm btn-outline-light ms-2" onclick="copyToClipboard(\`${round.response.replace(/`/g, '\\`')}\`)">
                                <i class="fas fa-copy"></i> 复制
                            </button>
                            <button class="btn btn-sm btn-outline-light ms-1" onclick="analyzeResponse(${round.round_number})">
                                <i class="fas fa-search"></i> 分析
                            </button>
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="response-content" style="max-height: 400px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: 'Courier New', monospace; font-size: 13px; line-height: 1.4;">
                                    ${round.response ? formatResponseContent(round.response) : '<span class="text-muted">无响应</span>'}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="response-stats">
                                    <h6 class="text-muted">响应统计</h6>
                                    <div class="small">
                                        <div class="d-flex justify-content-between">
                                            <span>字符数:</span>
                                            <span class="badge bg-info">${round.response ? round.response.length : 0}</span>
                                        </div>
                                        <div class="d-flex justify-content-between mt-1">
                                            <span>行数:</span>
                                            <span class="badge bg-info">${round.response ? round.response.split('\\n').length : 0}</span>
                                        </div>
                                        <div class="d-flex justify-content-between mt-1">
                                            <span>约Token:</span>
                                            <span class="badge bg-warning">${round.response ? Math.ceil(round.response.length / 4) : 0}</span>
                                        </div>
                                    </div>
                                    <div class="mt-3" id="response-analysis-${round.round_number}">
                                        <!-- 响应分析结果将显示在这里 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            ${round.model_config ? `
                <div class="mt-3">
                    <button class="btn btn-sm btn-outline-info" type="button" data-bs-toggle="collapse" data-bs-target="#config-${round.round_number}">
                        <i class="fas fa-cog"></i> 模型配置
                    </button>
                    <div class="collapse mt-2" id="config-${round.round_number}">
                        <pre class="code-block">${JSON.stringify(round.model_config, null, 2)}</pre>
                    </div>
                </div>
            ` : ''}
        </div>
        ${index < sortedRounds.length - 1 ? '<hr>' : ''}
    `).join('');

    container.innerHTML = html;
}

// 格式化提示词内容
function formatPromptContent(prompt) {
    if (!prompt) return '<span class="text-muted">无内容</span>';

    // 高亮关键词
    let formatted = prompt
        .replace(/\n/g, '<br>')
        .replace(/### (.*?)$/gm, '<strong class="text-primary">### $1</strong>')
        .replace(/## (.*?)$/gm, '<strong class="text-info">## $1</strong>')
        .replace(/# (.*?)$/gm, '<strong class="text-success"># $1</strong>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/`([^`]+)`/g, '<code class="bg-warning text-dark px-1">$1</code>')
        .replace(/```([^`]+)```/g, '<pre class="bg-dark text-light p-2 rounded mt-1 mb-1"><code>$1</code></pre>')
        .replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank" class="text-decoration-none">$1</a>');

    return formatted;
}

// 格式化响应内容
function formatResponseContent(response) {
    if (!response) return '<span class="text-muted">无响应</span>';

    // 高亮关键词和代码
    let formatted = response
        .replace(/\n/g, '<br>')
        .replace(/### (.*?)$/gm, '<strong class="text-primary">### $1</strong>')
        .replace(/## (.*?)$/gm, '<strong class="text-info">## $1</strong>')
        .replace(/# (.*?)$/gm, '<strong class="text-success"># $1</strong>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/`([^`]+)`/g, '<code class="bg-warning text-dark px-1">$1</code>')
        .replace(/```([^`]+)```/g, '<pre class="bg-dark text-light p-2 rounded mt-1 mb-1"><code>$1</code></pre>')
        .replace(/(ERROR|FAILED|失败|错误)/gi, '<span class="badge bg-danger">$1</span>')
        .replace(/(SUCCESS|成功|完成)/gi, '<span class="badge bg-success">$1</span>')
        .replace(/(WARNING|警告|注意)/gi, '<span class="badge bg-warning text-dark">$1</span>');

    return formatted;
}

// 分析提示词
function analyzePrompt(roundNumber) {
    const round = currentSession.rounds.find(r => r.round_number === roundNumber);
    if (!round || !round.prompt) return;

    const prompt = round.prompt;
    const analysisContainer = document.getElementById(`prompt-analysis-${roundNumber}`);

    // 分析提示词特征
    const analysis = {
        hasTaskDescription: /任务|task|目标|goal/i.test(prompt),
        hasConstraints: /约束|constraint|禁止|不要|必须/i.test(prompt),
        hasExamples: /示例|example|例如|比如/i.test(prompt),
        hasSteps: /步骤|step|第\d+步|首先|然后|最后/i.test(prompt),
        hasContext: /上下文|context|背景|项目/i.test(prompt),
        hasTools: /工具|tool|coordinator|使用/i.test(prompt),
        hasFormat: /格式|format|输出|返回/i.test(prompt),
        codeBlocks: (prompt.match(/```/g) || []).length / 2,
        questionMarks: (prompt.match(/\?/g) || []).length,
        exclamationMarks: (prompt.match(/!/g) || []).length
    };

    // 计算提示词质量分数
    let qualityScore = 0;
    if (analysis.hasTaskDescription) qualityScore += 20;
    if (analysis.hasConstraints) qualityScore += 15;
    if (analysis.hasExamples) qualityScore += 15;
    if (analysis.hasSteps) qualityScore += 20;
    if (analysis.hasContext) qualityScore += 10;
    if (analysis.hasTools) qualityScore += 10;
    if (analysis.hasFormat) qualityScore += 10;

    const qualityClass = qualityScore >= 80 ? 'success' : qualityScore >= 60 ? 'warning' : 'danger';

    analysisContainer.innerHTML = `
        <div class="card border-${qualityClass}">
            <div class="card-header bg-${qualityClass} text-white">
                <h6 class="mb-0">提示词质量分析</h6>
            </div>
            <div class="card-body p-2">
                <div class="mb-2">
                    <strong>质量分数: ${qualityScore}/100</strong>
                    <div class="progress mt-1" style="height: 8px;">
                        <div class="progress-bar bg-${qualityClass}" style="width: ${qualityScore}%"></div>
                    </div>
                </div>
                <div class="small">
                    <div class="d-flex justify-content-between">
                        <span>任务描述:</span>
                        <span class="${analysis.hasTaskDescription ? 'text-success' : 'text-danger'}">${analysis.hasTaskDescription ? '✓' : '✗'}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>约束条件:</span>
                        <span class="${analysis.hasConstraints ? 'text-success' : 'text-danger'}">${analysis.hasConstraints ? '✓' : '✗'}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>执行步骤:</span>
                        <span class="${analysis.hasSteps ? 'text-success' : 'text-danger'}">${analysis.hasSteps ? '✓' : '✗'}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>工具使用:</span>
                        <span class="${analysis.hasTools ? 'text-success' : 'text-danger'}">${analysis.hasTools ? '✓' : '✗'}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>代码块:</span>
                        <span class="badge bg-info">${analysis.codeBlocks}</span>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// 分析AI响应
function analyzeResponse(roundNumber) {
    const round = currentSession.rounds.find(r => r.round_number === roundNumber);
    if (!round || !round.response) return;

    const response = round.response;
    const analysisContainer = document.getElementById(`response-analysis-${roundNumber}`);

    // 分析响应特征
    const analysis = {
        hasCodeExecution: /```python|```javascript|```bash|tool_coordinator\./i.test(response),
        hasErrorHandling: /try|catch|except|error|失败|错误/i.test(response),
        hasExplanation: /因为|由于|原因|解释|说明/i.test(response),
        hasNextSteps: /下一步|接下来|然后|继续/i.test(response),
        hasResults: /结果|result|成功|完成|输出/i.test(response),
        isRelevant: !/(算法|数据结构|排序|查找)/i.test(response),
        codeBlocks: (response.match(/```/g) || []).length / 2,
        chineseRatio: (response.match(/[\u4e00-\u9fa5]/g) || []).length / response.length
    };

    // 计算响应质量分数
    let qualityScore = 0;
    if (analysis.hasCodeExecution) qualityScore += 25;
    if (analysis.hasErrorHandling) qualityScore += 15;
    if (analysis.hasExplanation) qualityScore += 20;
    if (analysis.hasResults) qualityScore += 20;
    if (analysis.isRelevant) qualityScore += 20;
    if (analysis.chineseRatio > 0.3) qualityScore += 10; // 中文回复加分

    const qualityClass = qualityScore >= 80 ? 'success' : qualityScore >= 60 ? 'warning' : 'danger';

    analysisContainer.innerHTML = `
        <div class="card border-${qualityClass}">
            <div class="card-header bg-${qualityClass} text-white">
                <h6 class="mb-0">响应质量分析</h6>
            </div>
            <div class="card-body p-2">
                <div class="mb-2">
                    <strong>质量分数: ${qualityScore}/100</strong>
                    <div class="progress mt-1" style="height: 8px;">
                        <div class="progress-bar bg-${qualityClass}" style="width: ${qualityScore}%"></div>
                    </div>
                </div>
                <div class="small">
                    <div class="d-flex justify-content-between">
                        <span>代码执行:</span>
                        <span class="${analysis.hasCodeExecution ? 'text-success' : 'text-danger'}">${analysis.hasCodeExecution ? '✓' : '✗'}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>错误处理:</span>
                        <span class="${analysis.hasErrorHandling ? 'text-success' : 'text-danger'}">${analysis.hasErrorHandling ? '✓' : '✗'}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>结果说明:</span>
                        <span class="${analysis.hasResults ? 'text-success' : 'text-danger'}">${analysis.hasResults ? '✓' : '✗'}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>任务相关:</span>
                        <span class="${analysis.isRelevant ? 'text-success' : 'text-danger'}">${analysis.isRelevant ? '✓' : '✗'}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>中文比例:</span>
                        <span class="badge bg-info">${(analysis.chineseRatio * 100).toFixed(1)}%</span>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// 复制到剪贴板
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showToast('内容已复制到剪贴板', 'success');
    }).catch(() => {
        showToast('复制失败', 'error');
    });
}

// 显示提示消息
function showToast(message, type = 'info') {
    const bgClass = type === 'success' ? 'bg-success' : type === 'error' ? 'bg-danger' : 'bg-info';

    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white ${bgClass} border-0 position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
        </div>
    `;
    document.body.appendChild(toast);

    setTimeout(() => {
        if (document.body.contains(toast)) {
            document.body.removeChild(toast);
        }
    }, 3000);
}

// 查看执行链路
function viewExecutionFlow() {
    if (!currentSession) {
        alert('会话信息未加载完成');
        return;
    }

    // 根据会话信息生成任务ID
    let taskId = currentSession.session_id;

    // 如果是作业失败分析任务，使用特殊的任务ID格式
    if (currentSession.task_type === 'job_failure_analysis' ||
        currentSession.task_title.includes('Job') ||
        currentSession.task_title.includes('失败分析')) {
        taskId = `job_failure_${currentSession.session_id}`;
    }

    // 跳转到执行链路页面，并传递任务ID
    const url = `/execution-flow?task_id=${encodeURIComponent(taskId)}&session_id=${encodeURIComponent(currentSession.session_id)}`;
    window.open(url, '_blank');
}

// 导出会话
function exportSession() {
    if (!currentSession) return;

    const data = {
        session_info: currentSession,
        export_time: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `session_${currentSession.session_id}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    showToast('会话数据已导出', 'success');
}
</script>
{% endblock %}
