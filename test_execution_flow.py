#!/usr/bin/env python3
"""
测试执行链路数据提取
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from web_ui.conversation_viewer_app import extract_execution_flow_from_logs

def test_real_data_extraction():
    """测试真实数据提取"""
    print("🧪 测试执行链路数据提取\n")
    
    # 测试真实的任务ID
    test_cases = [
        "job_failure_task_1748505049_1748505049",  # 应该找到真实数据
        "task_1748505049_1748505049",              # 直接使用session_id
        "unknown_task_123",                        # 应该使用模拟数据
    ]
    
    for task_id in test_cases:
        print(f"📋 测试任务ID: {task_id}")
        print("-" * 50)
        
        try:
            result = extract_execution_flow_from_logs(task_id)
            
            print(f"✅ 成功生成执行链路:")
            print(f"  - 任务类型: {result.get('task_type')}")
            print(f"  - 任务标题: {result.get('task_title')}")
            print(f"  - 步骤数量: {len(result.get('steps', []))}")
            print(f"  - 数据来源: {result.get('summary', {}).get('key_insight', 'unknown')}")
            
            # 检查是否是真实数据
            if "基于真实会话数据" in result.get('summary', {}).get('key_insight', ''):
                print(f"🎉 使用了真实会话数据！")
            else:
                print(f"⚠️  使用了模拟数据")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n")

if __name__ == "__main__":
    test_real_data_extraction()
