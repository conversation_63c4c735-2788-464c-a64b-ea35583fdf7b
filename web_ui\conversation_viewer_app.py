"""
对话分析Web界面
基于FastAPI的对话记录查看和分析系统
"""

import os
import sys
import re
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional

from fastapi import FastAPI, Request, HTTPException, Query
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import uvicorn

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bot_agent.utils.conversation_logger import ConversationLogger
from .error_log_collector import global_error_collector

app = FastAPI(title="Aider行为分析系统", description="Aider AI编程助手行为分析和优化平台")

# 设置模板和静态文件
templates = Jinja2Templates(directory="web_ui/templates")
app.mount("/static", StaticFiles(directory="web_ui/static"), name="static")

# 初始化对话记录器 - 使用正确的日志目录
import os
conversation_log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "logs", "conversations")
conversation_logger = ConversationLogger(log_dir=conversation_log_dir)
print(f"🔍 ConversationLogger初始化: {conversation_log_dir}")
print(f"📁 日志目录存在: {os.path.exists(conversation_log_dir)}")


# 清空日志的辅助函数
async def clear_conversation_logs(days_to_keep: int = 0) -> int:
    """清空对话日志"""
    import shutil
    from pathlib import Path

    cleared_count = 0
    log_dir = Path(conversation_log_dir)

    if not log_dir.exists():
        return 0

    cutoff_date = datetime.now() - timedelta(days=days_to_keep) if days_to_keep > 0 else None

    for date_dir in log_dir.iterdir():
        if date_dir.is_dir():
            try:
                # 解析日期目录名 (YYYY-MM-DD)
                dir_date = datetime.strptime(date_dir.name, "%Y-%m-%d")

                # 如果需要保留最近几天的日志
                if cutoff_date and dir_date >= cutoff_date:
                    continue

                # 删除整个日期目录
                shutil.rmtree(date_dir)
                cleared_count += 1
                print(f"🗑️ 删除对话日志目录: {date_dir}")

            except ValueError:
                # 如果目录名不是日期格式，跳过
                continue
            except Exception as e:
                print(f"❌ 删除对话日志目录失败 {date_dir}: {e}")
                continue

    return cleared_count


async def clear_error_logs(days_to_keep: int = 0) -> int:
    """清空错误日志"""
    import glob

    cleared_count = 0

    # 错误日志通常在logs目录下
    logs_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "logs")

    if not os.path.exists(logs_dir):
        return 0

    cutoff_date = datetime.now() - timedelta(days=days_to_keep) if days_to_keep > 0 else None

    # 查找所有日志文件
    log_patterns = [
        os.path.join(logs_dir, "*.log"),
        os.path.join(logs_dir, "**", "*.log"),
        os.path.join(logs_dir, "error_*.txt"),
        os.path.join(logs_dir, "**", "error_*.txt")
    ]

    for pattern in log_patterns:
        for log_file in glob.glob(pattern, recursive=True):
            try:
                # 检查文件修改时间
                file_mtime = datetime.fromtimestamp(os.path.getmtime(log_file))

                # 如果需要保留最近几天的日志
                if cutoff_date and file_mtime >= cutoff_date:
                    continue

                # 删除日志文件
                os.remove(log_file)
                cleared_count += 1
                print(f"🗑️ 删除错误日志文件: {log_file}")

            except Exception as e:
                print(f"❌ 删除错误日志文件失败 {log_file}: {e}")
                continue

    return cleared_count


async def clear_system_logs(days_to_keep: int = 0) -> int:
    """清空系统日志"""
    import glob

    cleared_count = 0

    # 系统日志路径
    system_log_paths = [
        os.path.join(os.path.dirname(os.path.dirname(__file__)), "logs"),
        os.path.join(os.path.dirname(os.path.dirname(__file__)), "*.log"),
        "/tmp/aider-plus-*.log",  # Linux临时日志
        "C:\\temp\\aider-plus-*.log"  # Windows临时日志
    ]

    cutoff_date = datetime.now() - timedelta(days=days_to_keep) if days_to_keep > 0 else None

    for log_path in system_log_paths:
        try:
            if "*" in log_path:
                # 通配符路径
                for log_file in glob.glob(log_path):
                    try:
                        # 检查文件修改时间
                        file_mtime = datetime.fromtimestamp(os.path.getmtime(log_file))

                        # 如果需要保留最近几天的日志
                        if cutoff_date and file_mtime >= cutoff_date:
                            continue

                        # 删除日志文件
                        os.remove(log_file)
                        cleared_count += 1
                        print(f"🗑️ 删除系统日志文件: {log_file}")

                    except Exception as e:
                        print(f"❌ 删除系统日志文件失败 {log_file}: {e}")
                        continue
            else:
                # 目录路径
                if os.path.exists(log_path) and os.path.isdir(log_path):
                    for root, dirs, files in os.walk(log_path):
                        for file in files:
                            if file.endswith(('.log', '.txt')) and 'system' in file.lower():
                                file_path = os.path.join(root, file)
                                try:
                                    # 检查文件修改时间
                                    file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))

                                    # 如果需要保留最近几天的日志
                                    if cutoff_date and file_mtime >= cutoff_date:
                                        continue

                                    # 删除日志文件
                                    os.remove(file_path)
                                    cleared_count += 1
                                    print(f"🗑️ 删除系统日志文件: {file_path}")

                                except Exception as e:
                                    print(f"❌ 删除系统日志文件失败 {file_path}: {e}")
                                    continue
        except Exception as e:
            print(f"❌ 处理系统日志路径失败 {log_path}: {e}")
            continue

    return cleared_count


@app.get("/", response_class=HTMLResponse)
async def dashboard(request: Request):
    """主仪表板"""
    return templates.TemplateResponse("dashboard.html", {"request": request})


@app.get("/api/sessions")
async def get_sessions(
    task_type: Optional[str] = Query(None, description="任务类型过滤"),
    status: Optional[str] = Query(None, description="状态过滤"),
    date_from: Optional[str] = Query(None, description="开始日期"),
    date_to: Optional[str] = Query(None, description="结束日期"),
    limit: int = Query(50, description="返回数量限制")
):
    """获取会话列表API"""
    try:
        sessions = conversation_logger.search_sessions(
            task_type=task_type,
            status=status,
            date_from=date_from,
            date_to=date_to
        )

        # 限制返回数量
        sessions = sessions[:limit]

        return JSONResponse({
            "success": True,
            "data": sessions,
            "total": len(sessions)
        })
    except Exception as e:
        return JSONResponse({
            "success": False,
            "error": str(e)
        }, status_code=500)


@app.get("/api/sessions/{session_id}")
async def get_session_detail(session_id: str):
    """获取会话详情API"""
    try:
        session = conversation_logger._load_session_from_file(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="会话未找到")

        # 转换为字典格式
        session_dict = {
            "session_id": session.session_id,
            "task_id": session.task_id,
            "task_title": session.task_title,
            "task_type": session.task_type,
            "project_path": session.project_path,
            "started_at": session.started_at,
            "ended_at": session.ended_at,
            "total_duration": session.total_duration,
            "status": session.status if isinstance(session.status, str) else session.status.value,
            "final_result": session.final_result,
            "error_summary": session.error_summary,
            "metadata": session.metadata,
            "rounds": []
        }

        # 转换轮次数据
        for round_info in session.rounds:
            round_dict = {
                "round_number": round_info.round_number,
                "round_name": round_info.round_name,
                "prompt": round_info.prompt,
                "response": round_info.response,
                "model_name": round_info.model_name,
                "timestamp": round_info.timestamp,
                "duration": round_info.duration,
                "status": round_info.status if isinstance(round_info.status, str) else round_info.status.value,
                "retry_count": round_info.retry_count,
                "error_message": round_info.error_message,
                "token_usage": round_info.token_usage,
                "model_config": round_info.model_config
            }
            session_dict["rounds"].append(round_dict)

        return JSONResponse({
            "success": True,
            "data": session_dict
        })
    except Exception as e:
        return JSONResponse({
            "success": False,
            "error": str(e)
        }, status_code=500)


@app.get("/api/statistics")
async def get_statistics(
    days: int = Query(7, description="统计天数")
):
    """获取统计信息API"""
    try:
        # 计算日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        date_from = start_date.strftime("%Y-%m-%d")
        date_to = end_date.strftime("%Y-%m-%d")

        # 获取会话数据
        sessions = conversation_logger.search_sessions(
            date_from=date_from,
            date_to=date_to
        )

        # 计算统计信息
        total_sessions = len(sessions)
        success_sessions = len([s for s in sessions if s['status'] == 'success'])
        failed_sessions = len([s for s in sessions if s['status'] == 'failed'])
        in_progress_sessions = len([s for s in sessions if s['status'] == 'in_progress'])

        # 按任务类型统计
        task_types = {}
        for session in sessions:
            task_type = session['task_type']
            if task_type not in task_types:
                task_types[task_type] = {'total': 0, 'success': 0, 'failed': 0}
            task_types[task_type]['total'] += 1
            if session['status'] == 'success':
                task_types[task_type]['success'] += 1
            elif session['status'] == 'failed':
                task_types[task_type]['failed'] += 1

        # 按日期统计
        daily_stats = {}
        for session in sessions:
            date = session['started_at'][:10]  # YYYY-MM-DD
            if date not in daily_stats:
                daily_stats[date] = {'total': 0, 'success': 0, 'failed': 0}
            daily_stats[date]['total'] += 1
            if session['status'] == 'success':
                daily_stats[date]['success'] += 1
            elif session['status'] == 'failed':
                daily_stats[date]['failed'] += 1

        # 平均轮次统计
        total_rounds = sum(session['rounds_count'] for session in sessions)
        avg_rounds = total_rounds / total_sessions if total_sessions > 0 else 0

        # 执行时长分布统计
        duration_distribution = {
            "0-30秒": 0,
            "30秒-2分钟": 0,
            "2-5分钟": 0,
            "5-10分钟": 0,
            "10分钟以上": 0
        }

        total_duration = 0
        sessions_with_duration = 0

        for session in sessions:
            # 计算会话持续时间
            if session.get('ended_at') and session.get('started_at'):
                try:
                    # 处理时间格式
                    started_at = session['started_at']
                    ended_at = session['ended_at']

                    # 处理不同的时间格式
                    if started_at.endswith('Z'):
                        # 有Z后缀，移除并添加时区
                        started_at = started_at[:-1] + '+00:00'
                    elif '+' not in started_at and 'T' in started_at:
                        # 没有时区信息的ISO格式，假设为本地时间
                        pass  # 直接使用fromisoformat

                    if ended_at.endswith('Z'):
                        # 有Z后缀，移除并添加时区
                        ended_at = ended_at[:-1] + '+00:00'
                    elif '+' not in ended_at and 'T' in ended_at:
                        # 没有时区信息的ISO格式，假设为本地时间
                        pass  # 直接使用fromisoformat

                    start_time = datetime.fromisoformat(started_at)
                    end_time = datetime.fromisoformat(ended_at)
                    duration_seconds = (end_time - start_time).total_seconds()

                    # 只统计正数时长
                    if duration_seconds > 0:
                        total_duration += duration_seconds
                        sessions_with_duration += 1

                        # 分类到时长区间
                        if duration_seconds <= 30:
                            duration_distribution["0-30秒"] += 1
                        elif duration_seconds <= 120:  # 2分钟
                            duration_distribution["30秒-2分钟"] += 1
                        elif duration_seconds <= 300:  # 5分钟
                            duration_distribution["2-5分钟"] += 1
                        elif duration_seconds <= 600:  # 10分钟
                            duration_distribution["5-10分钟"] += 1
                        else:
                            duration_distribution["10分钟以上"] += 1

                except Exception as e:
                    # 如果时间解析失败，跳过这个会话
                    print(f"时间解析失败: {session.get('session_id', 'unknown')} - {e}")
                    continue
            else:
                # 如果没有ended_at，检查是否是进行中的会话
                if session.get('started_at') and session.get('status') == 'in_progress':
                    try:
                        started_at = session['started_at']
                        if started_at.endswith('Z'):
                            started_at = started_at[:-1] + '+00:00'

                        start_time = datetime.fromisoformat(started_at)
                        current_time = datetime.now(start_time.tzinfo)
                        duration_seconds = (current_time - start_time).total_seconds()

                        if duration_seconds > 0:
                            total_duration += duration_seconds
                            sessions_with_duration += 1

                            # 分类到时长区间
                            if duration_seconds <= 30:
                                duration_distribution["0-30秒"] += 1
                            elif duration_seconds <= 120:
                                duration_distribution["30秒-2分钟"] += 1
                            elif duration_seconds <= 300:
                                duration_distribution["2-5分钟"] += 1
                            elif duration_seconds <= 600:
                                duration_distribution["5-10分钟"] += 1
                            else:
                                duration_distribution["10分钟以上"] += 1

                    except Exception as e:
                        print(f"进行中会话时间解析失败: {session.get('session_id', 'unknown')} - {e}")
                        continue

        avg_duration = total_duration / sessions_with_duration if sessions_with_duration > 0 else 0

        # 调试信息
        print(f"统计调试: 总会话数={total_sessions}, 有时长数据的会话数={sessions_with_duration}")
        print(f"时长分布: {duration_distribution}")

        return JSONResponse({
            "success": True,
            "data": {
                "summary": {
                    "total_sessions": total_sessions,
                    "success_sessions": success_sessions,
                    "failed_sessions": failed_sessions,
                    "in_progress_sessions": in_progress_sessions,
                    "success_rate": (success_sessions / total_sessions * 100) if total_sessions > 0 else 0,
                    "total_rounds": total_rounds,
                    "avg_rounds": round(avg_rounds, 2),
                    "avg_duration": round(avg_duration, 2),
                    "sessions_with_duration": sessions_with_duration
                },
                "task_types": task_types,
                "daily_stats": daily_stats,
                "duration_distribution": duration_distribution,
                "date_range": {
                    "from": date_from,
                    "to": date_to,
                    "days": days
                }
            }
        })
    except Exception as e:
        return JSONResponse({
            "success": False,
            "error": str(e)
        }, status_code=500)


@app.get("/sessions", response_class=HTMLResponse)
async def sessions_page(request: Request):
    """会话列表页面"""
    return templates.TemplateResponse("sessions.html", {"request": request})


@app.get("/sessions/{session_id}", response_class=HTMLResponse)
async def session_detail_page(request: Request, session_id: str):
    """会话详情页面"""
    return templates.TemplateResponse("session_detail.html", {
        "request": request,
        "session_id": session_id
    })


@app.get("/statistics", response_class=HTMLResponse)
async def statistics_page(request: Request):
    """统计分析页面"""
    return templates.TemplateResponse("statistics.html", {"request": request})


@app.get("/analysis-demo", response_class=HTMLResponse)
async def analysis_demo_page(request: Request):
    """分析链路演示页面"""
    return templates.TemplateResponse("analysis_demo.html", {"request": request})


@app.get("/api/errors")
async def get_errors(
    hours: int = Query(24, description="收集最近几小时的错误"),
    level: Optional[str] = Query(None, description="错误级别过滤"),
    logger_name: Optional[str] = Query(None, description="日志器名称过滤"),
    exception_type: Optional[str] = Query(None, description="异常类型过滤"),
    query: Optional[str] = Query(None, description="搜索查询"),
    limit: int = Query(100, description="返回数量限制")
):
    """获取错误日志API"""
    try:
        # 收集所有错误
        all_errors = global_error_collector.collect_all_errors(hours=hours)

        # 搜索过滤
        filtered_errors = global_error_collector.search_errors(
            all_errors,
            query=query,
            level=level,
            logger_name=logger_name,
            exception_type=exception_type
        )

        # 限制返回数量
        limited_errors = filtered_errors[:limit]

        # 转换为字典格式
        error_dicts = []
        for error in limited_errors:
            error_dict = {
                "timestamp": error.timestamp,
                "level": error.level,
                "logger_name": error.logger_name,
                "message": error.message,
                "file_path": error.file_path,
                "line_number": error.line_number,
                "function_name": error.function_name,
                "exception_type": error.exception_type,
                "exception_message": error.exception_message,
                "traceback": error.traceback,
                "context": error.context,
                "source_file": error.source_file,
                "session_id": error.session_id,
                "task_id": error.task_id
            }
            error_dicts.append(error_dict)

        return JSONResponse({
            "success": True,
            "data": error_dicts,
            "total": len(filtered_errors),
            "collected_total": len(all_errors)
        })
    except Exception as e:
        return JSONResponse({
            "success": False,
            "error": str(e)
        }, status_code=500)


@app.get("/api/errors/statistics")
async def get_error_statistics(
    hours: int = Query(24, description="统计最近几小时的错误")
):
    """获取错误统计信息API"""
    try:
        # 收集错误
        all_errors = global_error_collector.collect_all_errors(hours=hours)

        # 获取统计信息
        stats = global_error_collector.get_error_statistics(all_errors)

        return JSONResponse({
            "success": True,
            "data": stats
        })
    except Exception as e:
        return JSONResponse({
            "success": False,
            "error": str(e)
        }, status_code=500)


@app.get("/errors", response_class=HTMLResponse)
async def errors_page(request: Request):
    """错误日志监控页面"""
    return templates.TemplateResponse("errors.html", {"request": request})


@app.post("/api/logs/clear")
async def clear_logs(request: Request):
    """清空日志API"""
    try:
        data = await request.json()
        log_type = data.get('log_type', 'all')  # all, conversations, errors
        days_to_keep = data.get('days_to_keep', 0)  # 保留最近几天的日志，0表示全部清空

        result = {
            "success": True,
            "message": "",
            "details": {}
        }

        # 清空对话日志
        if log_type in ['all', 'conversations']:
            try:
                cleared_count = await clear_conversation_logs(days_to_keep)
                result["details"]["conversations"] = {
                    "cleared_count": cleared_count,
                    "status": "success"
                }
                result["message"] += f"清空了 {cleared_count} 个对话日志文件。"
            except Exception as e:
                result["details"]["conversations"] = {
                    "error": str(e),
                    "status": "failed"
                }
                result["message"] += f"对话日志清空失败: {str(e)}。"

        # 清空错误日志
        if log_type in ['all', 'errors']:
            try:
                cleared_count = await clear_error_logs(days_to_keep)
                result["details"]["errors"] = {
                    "cleared_count": cleared_count,
                    "status": "success"
                }
                result["message"] += f"清空了 {cleared_count} 个错误日志文件。"
            except Exception as e:
                result["details"]["errors"] = {
                    "error": str(e),
                    "status": "failed"
                }
                result["message"] += f"错误日志清空失败: {str(e)}。"

        # 清空系统日志
        if log_type in ['all', 'system']:
            try:
                cleared_count = await clear_system_logs(days_to_keep)
                result["details"]["system"] = {
                    "cleared_count": cleared_count,
                    "status": "success"
                }
                result["message"] += f"清空了 {cleared_count} 个系统日志文件。"
            except Exception as e:
                result["details"]["system"] = {
                    "error": str(e),
                    "status": "failed"
                }
                result["message"] += f"系统日志清空失败: {str(e)}。"

        return JSONResponse(result)

    except Exception as e:
        return JSONResponse({
            "success": False,
            "error": f"清空日志失败: {str(e)}"
        }, status_code=500)


@app.post("/api/analyze-prompt")
async def analyze_prompt(request: Request):
    """分析提示词并提供优化建议"""
    try:
        data = await request.json()
        prompt = data.get('prompt', '')
        response = data.get('response', '')

        if not prompt:
            return JSONResponse({
                "success": False,
                "error": "提示词内容不能为空"
            }, status_code=400)

        # 分析提示词特征
        analysis = {
            "basic_stats": {
                "length": len(prompt),
                "lines": len(prompt.split('\n')),
                "estimated_tokens": len(prompt) // 4,
                "chinese_ratio": len([c for c in prompt if '\u4e00' <= c <= '\u9fa5']) / len(prompt) if prompt else 0
            },
            "structure_analysis": {
                "has_task_description": bool(re.search(r'任务|task|目标|goal', prompt, re.I)),
                "has_constraints": bool(re.search(r'约束|constraint|禁止|不要|必须', prompt, re.I)),
                "has_examples": bool(re.search(r'示例|example|例如|比如', prompt, re.I)),
                "has_steps": bool(re.search(r'步骤|step|第\d+步|首先|然后|最后', prompt, re.I)),
                "has_context": bool(re.search(r'上下文|context|背景|项目', prompt, re.I)),
                "has_tools": bool(re.search(r'工具|tool|coordinator|使用', prompt, re.I)),
                "has_format": bool(re.search(r'格式|format|输出|返回', prompt, re.I)),
                "code_blocks": len(re.findall(r'```', prompt)) // 2,
                "headings": len(re.findall(r'^#+\s', prompt, re.M))
            },
            "quality_issues": [],
            "suggestions": []
        }

        # 质量问题检测
        if not analysis["structure_analysis"]["has_task_description"]:
            analysis["quality_issues"].append("缺少明确的任务描述")
            analysis["suggestions"].append("添加清晰的任务目标和期望结果")

        if not analysis["structure_analysis"]["has_constraints"]:
            analysis["quality_issues"].append("缺少约束条件")
            analysis["suggestions"].append("添加明确的约束条件，如禁止事项、必须遵循的规则等")

        if not analysis["structure_analysis"]["has_steps"]:
            analysis["quality_issues"].append("缺少执行步骤")
            analysis["suggestions"].append("提供清晰的执行步骤，帮助AI理解任务流程")

        if analysis["basic_stats"]["length"] < 100:
            analysis["quality_issues"].append("提示词过短")
            analysis["suggestions"].append("增加更多上下文信息和详细说明")
        elif analysis["basic_stats"]["length"] > 4000:
            analysis["quality_issues"].append("提示词过长")
            analysis["suggestions"].append("精简内容，突出重点信息")

        if analysis["basic_stats"]["chinese_ratio"] < 0.3:
            analysis["quality_issues"].append("中文内容较少")
            analysis["suggestions"].append("增加中文说明，提高AI理解准确性")

        # 如果有响应，分析响应质量
        response_analysis = None
        if response:
            response_analysis = {
                "basic_stats": {
                    "length": len(response),
                    "lines": len(response.split('\n')),
                    "estimated_tokens": len(response) // 4,
                    "chinese_ratio": len([c for c in response if '\u4e00' <= c <= '\u9fa5']) / len(response)
                },
                "content_analysis": {
                    "has_code_execution": bool(re.search(r'```python|```javascript|```bash|tool_coordinator\.', response, re.I)),
                    "has_error_handling": bool(re.search(r'try|catch|except|error|失败|错误', response, re.I)),
                    "has_explanation": bool(re.search(r'因为|由于|原因|解释|说明', response, re.I)),
                    "has_results": bool(re.search(r'结果|result|成功|完成|输出', response, re.I)),
                    "is_relevant": not bool(re.search(r'算法|数据结构|排序|查找', response, re.I)),
                    "code_blocks": len(re.findall(r'```', response)) // 2
                },
                "quality_score": 0
            }

            # 计算响应质量分数
            if response_analysis["content_analysis"]["has_code_execution"]:
                response_analysis["quality_score"] += 25
            if response_analysis["content_analysis"]["has_error_handling"]:
                response_analysis["quality_score"] += 15
            if response_analysis["content_analysis"]["has_explanation"]:
                response_analysis["quality_score"] += 20
            if response_analysis["content_analysis"]["has_results"]:
                response_analysis["quality_score"] += 20
            if response_analysis["content_analysis"]["is_relevant"]:
                response_analysis["quality_score"] += 20

        # 计算总体质量分数
        quality_score = 0
        max_score = 100

        if analysis["structure_analysis"]["has_task_description"]:
            quality_score += 20
        if analysis["structure_analysis"]["has_constraints"]:
            quality_score += 15
        if analysis["structure_analysis"]["has_examples"]:
            quality_score += 15
        if analysis["structure_analysis"]["has_steps"]:
            quality_score += 20
        if analysis["structure_analysis"]["has_context"]:
            quality_score += 10
        if analysis["structure_analysis"]["has_tools"]:
            quality_score += 10
        if analysis["structure_analysis"]["has_format"]:
            quality_score += 10

        analysis["quality_score"] = quality_score
        analysis["quality_level"] = "优秀" if quality_score >= 80 else "良好" if quality_score >= 60 else "需要改进"

        return JSONResponse({
            "success": True,
            "data": {
                "prompt_analysis": analysis,
                "response_analysis": response_analysis,
                "overall_assessment": {
                    "prompt_quality": analysis["quality_level"],
                    "main_issues": analysis["quality_issues"][:3],  # 最多显示3个主要问题
                    "top_suggestions": analysis["suggestions"][:3]  # 最多显示3个建议
                }
            }
        })

    except Exception as e:
        return JSONResponse({
            "success": False,
            "error": str(e)
        }, status_code=500)


@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}


@app.get("/execution-flow", response_class=HTMLResponse)
async def execution_flow_viewer(request: Request):
    """执行链路可视化页面"""
    return templates.TemplateResponse("execution_flow_viewer.html", {"request": request})


@app.get("/api/available-tasks")
async def get_available_tasks():
    """获取所有可用的任务列表用于执行链路选择"""
    try:
        # 获取所有会话
        sessions = conversation_logger.search_sessions()

        # 转换为任务选项格式
        tasks = []
        for session in sessions[:20]:  # 限制最多20个
            task_id = session.get('session_id', '')
            task_title = session.get('task_title', '未知任务')
            task_type = session.get('task_type', 'unknown')
            started_at = session.get('started_at', '')

            # 格式化显示名称
            if started_at:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(started_at.replace('Z', '+00:00'))
                    date_str = dt.strftime('%m-%d %H:%M')
                except:
                    date_str = started_at[:16]
            else:
                date_str = '未知时间'

            display_name = f"{task_title} ({date_str})"

            tasks.append({
                "task_id": task_id,
                "display_name": display_name,
                "task_title": task_title,
                "task_type": task_type,
                "started_at": started_at
            })

        return JSONResponse({
            "success": True,
            "data": tasks,
            "total": len(tasks)
        })
    except Exception as e:
        print(f"❌ 获取可用任务失败: {e}")
        return JSONResponse({
            "success": False,
            "error": str(e),
            "data": []
        }, status_code=500)


@app.get("/api/execution-flow/{task_id}")
async def get_execution_flow(task_id: str):
    """获取任务执行链路数据"""
    try:
        print(f"🚀 API调用: 获取执行链路 task_id={task_id}")

        # 从任务日志中提取执行链路信息
        execution_data = extract_execution_flow_from_logs(task_id)

        print(f"📊 执行链路数据生成完成: task_type={execution_data.get('task_type')}, steps={len(execution_data.get('steps', []))}")

        return JSONResponse({
            "success": True,
            "data": execution_data,
            "debug_info": {
                "task_id": task_id,
                "data_source": "real_session" if execution_data.get('summary', {}).get('key_insight', '').startswith('基于真实会话数据') else "mock_data",
                "timestamp": datetime.now().isoformat()
            }
        })
    except Exception as e:
        print(f"❌ 获取执行链路失败: {e}")
        import traceback
        traceback.print_exc()
        return JSONResponse({
            "success": False,
            "error": str(e),
            "debug_info": {
                "task_id": task_id,
                "error_type": type(e).__name__,
                "timestamp": datetime.now().isoformat()
            }
        }, status_code=500)


def generate_execution_flow_from_session(session_data: dict, task_id: str) -> dict:
    """从会话数据生成执行链路"""

    rounds = session_data.get('rounds', [])
    if not rounds:
        return generate_default_execution_flow(task_id)

    # 分析会话数据，生成执行步骤
    steps = []
    step_id = 1

    # 第一步：任务初始化
    steps.append({
        "id": step_id,
        "name": "任务初始化",
        "type": "automated",
        "status": "success",
        "description": f"初始化{session_data.get('task_type', '未知')}任务",
        "input": f"task_type: {session_data.get('task_type', 'unknown')}",
        "output": "任务环境准备完成",
        "duration": "0.5s",
        "started_at": session_data.get('started_at', ''),
        "ended_at": session_data.get('started_at', ''),
        "details": {
            "session_id": session_data.get('session_id'),
            "task_title": session_data.get('task_title', ''),
            "project_path": session_data.get('project_path', ''),
            "api_key_required": False,
            "local_processing": True
        }
    })
    step_id += 1

    # 分析每个对话轮次
    for round_data in sorted(rounds, key=lambda x: x.get('round_number', 0)):
        round_name = round_data.get('round_name', f"第{round_data.get('round_number', 0)}轮")

        # 判断是否是真实AI调用
        model_name = round_data.get('model_name', '')
        token_usage = round_data.get('token_usage')

        # 系统处理器列表（这些是内部处理器，不是真实AI）
        system_processors = [
            'intelligent-job-analyzer', 'fix-verifier', 'second-round-precise-fixer',
            'second-round-fixer', 'job-analyzer', 'error-analyzer', 'fix-executor'
        ]

        # 真实AI模型特征检测
        is_real_ai = False

        # 1. 首先排除系统处理器
        if model_name not in system_processors:
            # 2. 检测真实AI模型特征
            is_real_ai = (
                # 包含AI服务提供商名称
                any(provider in model_name.lower() for provider in ['deepseek', 'gpt', 'claude', 'openai', 'anthropic']) or
                # 包含模型路径格式 (如 deepseek/deepseek-r1:free)
                '/' in model_name or
                # 标准模型名称格式
                model_name.startswith(('gpt-', 'claude-', 'deepseek-')) or
                # 有token使用记录
                token_usage is not None or
                # 执行时间较长（真实AI调用通常比较慢）
                round_data.get('duration', 0) > 5.0
            )

        # 获取处理状态
        step_status = "success" if round_data.get('status') == 'success' else "failed"

        # 根据类型生成步骤
        if is_real_ai:
            # 真实AI接口调用
            ai_step = {
                "id": step_id,
                "name": f"AI接口调用 - {round_name}",
                "type": "ai",
                "status": step_status,
                "description": f"调用{model_name}处理{round_name}",
                "input": f"提示词: {len(round_data.get('prompt', ''))} 字符",
                "output": f"AI响应: {len(round_data.get('response', ''))} 字符" if step_status == "success" else f"调用失败: {round_data.get('error_message', '未知错误')}",
                "duration": f"{round_data.get('duration', 0):.1f}s",
                "started_at": round_data.get('timestamp', ''),
                "ended_at": round_data.get('timestamp', ''),
                "details": {
                    "model_name": model_name,
                    "token_usage": token_usage or {},
                    "retry_count": round_data.get('retry_count', 0),
                    "api_key_required": True,
                    "local_processing": False,
                    "api_provider": "真实AI服务",
                    "prompt_preview": round_data.get('prompt', ''),  # 保存完整内容
                    "response_preview": round_data.get('response', ''),  # 保存完整内容
                    "prompt_length": len(round_data.get('prompt', '')),
                    "response_length": len(round_data.get('response', ''))
                }
            }
        else:
            # 系统内部处理器
            ai_step = {
                "id": step_id,
                "name": f"系统处理器 - {round_name}",
                "type": "automated",
                "status": step_status,
                "description": f"系统内部{model_name}处理{round_name}",
                "input": f"处理请求: {len(round_data.get('prompt', ''))} 字符",
                "output": f"处理结果: {len(round_data.get('response', ''))} 字符" if step_status == "success" else f"处理失败: {round_data.get('error_message', '未知错误')}",
                "duration": f"{round_data.get('duration', 0):.1f}s",
                "started_at": round_data.get('timestamp', ''),
                "ended_at": round_data.get('timestamp', ''),
                "details": {
                    "processor_name": model_name,
                    "processor_type": "内部逻辑处理器",
                    "retry_count": round_data.get('retry_count', 0),
                    "api_key_required": False,
                    "local_processing": True,
                    "processing_method": "本地算法和规则",
                    "input_preview": round_data.get('prompt', ''),  # 保存完整内容
                    "output_preview": round_data.get('response', ''),  # 保存完整内容
                    "input_length": len(round_data.get('prompt', '')),
                    "output_length": len(round_data.get('response', ''))
                }
            }

        if ai_step["status"] == "failed":
            ai_step["error"] = round_data.get('error_message', '未知错误')

        steps.append(ai_step)
        step_id += 1

        # 如果处理成功，添加后续处理步骤
        if ai_step["status"] == "success":
            response = round_data.get('response', '')

            # 检查是否包含工具调用或代码执行
            if any(keyword in response.lower() for keyword in ['tool', 'command', 'execute', '执行', '工具', '命令']):
                steps.append({
                    "id": step_id,
                    "name": f"执行操作建议 - {round_name}",
                    "type": "automated",
                    "status": "success",
                    "description": "执行生成的操作建议",
                    "input": "操作指令",
                    "output": "操作执行完成",
                    "duration": "2.0s",
                    "details": {
                        "execution_type": "suggested_actions",
                        "api_key_required": False,
                        "local_processing": True
                    }
                })
                step_id += 1

    # 计算统计信息
    total_steps = len(steps)
    ai_steps = len([s for s in steps if s['type'] == 'ai'])
    automated_steps = len([s for s in steps if s['type'] == 'automated'])
    fallback_steps = len([s for s in steps if s['type'] == 'fallback'])
    success_steps = len([s for s in steps if s['status'] == 'success'])

    return {
        "task_id": task_id,
        "task_type": session_data.get('task_type', 'unknown'),
        "task_title": session_data.get('task_title', '未知任务'),
        "started_at": session_data.get('started_at', ''),
        "ended_at": session_data.get('ended_at', ''),
        "total_duration": session_data.get('total_duration', '未知'),
        "steps": steps,
        "summary": {
            "total_steps": total_steps,
            "ai_steps": ai_steps,
            "automated_steps": automated_steps,
            "fallback_steps": fallback_steps,
            "success_rate": f"{(success_steps / total_steps * 100):.1f}%" if total_steps > 0 else "0%",
            "ai_dependency": "高" if ai_steps > automated_steps else "中" if ai_steps > 0 else "低",
            "main_bottleneck": "AI交互" if ai_steps > 0 else "无",
            "key_insight": f"基于真实会话数据生成，包含{len(rounds)}轮对话"
        }
    }

def generate_default_execution_flow(task_id: str) -> dict:
    """生成默认的执行链路（当没有会话数据时）"""
    return {
        "task_id": task_id,
        "task_type": "unknown",
        "task_title": "未知任务",
        "started_at": datetime.now().isoformat(),
        "ended_at": datetime.now().isoformat(),
        "total_duration": "0s",
        "steps": [
            {
                "id": 1,
                "name": "任务查找",
                "type": "automated",
                "status": "failed",
                "description": "尝试查找任务执行记录",
                "input": f"task_id: {task_id}",
                "output": "未找到对应的任务记录",
                "duration": "0.1s",
                "details": {
                    "api_key_required": False,
                    "local_processing": True,
                    "search_locations": ["会话日志", "任务日志", "执行记录"]
                }
            }
        ],
        "summary": {
            "total_steps": 1,
            "ai_steps": 0,
            "automated_steps": 1,
            "fallback_steps": 0,
            "success_rate": "0%",
            "ai_dependency": "无",
            "main_bottleneck": "数据缺失",
            "key_insight": "未找到对应的任务执行记录"
        }
    }

def extract_execution_flow_from_logs(task_id: str) -> dict:
    """从日志中提取执行链路信息"""

    # 尝试从会话数据中提取执行链路
    if any(task_id.startswith(prefix) for prefix in ['task_', 'job_failure_', 'ai_inquiry_', 'test_']):
        # 提取session_id - 修复逻辑
        if task_id.startswith('job_failure_task_'):
            # 格式: job_failure_task_1748505049_1748505049 -> task_1748505049_1748505049
            session_id = task_id.replace('job_failure_', '')
        elif task_id.startswith('job_failure_'):
            # 格式: job_failure_ai_inquiry_1748494936_1748494936 -> ai_inquiry_1748494936_1748494936
            session_id = task_id.replace('job_failure_', '')
        else:
            # 其他格式，直接使用 (task_, ai_inquiry_, test_等)
            session_id = task_id

        print(f"🔍 尝试查找会话数据: task_id={task_id}, session_id={session_id}")

        try:
            # 从会话数据中生成执行链路
            session_obj = conversation_logger._load_session_from_file(session_id)
            print(f"📊 会话数据查找结果: {session_obj is not None}")

            if session_obj:
                print(f"✅ 找到会话数据，包含 {len(session_obj.rounds)} 轮对话")

                # 转换为字典格式
                session_data = {
                    "session_id": session_obj.session_id,
                    "task_id": session_obj.task_id,
                    "task_title": session_obj.task_title,
                    "task_type": session_obj.task_type,
                    "project_path": session_obj.project_path,
                    "started_at": session_obj.started_at,
                    "ended_at": session_obj.ended_at,
                    "total_duration": session_obj.total_duration,
                    "status": session_obj.status if isinstance(session_obj.status, str) else session_obj.status.value,
                    "final_result": session_obj.final_result,
                    "error_summary": session_obj.error_summary,
                    "metadata": session_obj.metadata,
                    "rounds": []
                }

                # 转换轮次数据
                for round_obj in session_obj.rounds:
                    round_data = {
                        "round_number": round_obj.round_number,
                        "round_name": round_obj.round_name,
                        "prompt": round_obj.prompt,
                        "response": round_obj.response,
                        "model_name": round_obj.model_name,
                        "timestamp": round_obj.timestamp,
                        "duration": round_obj.duration,
                        "status": round_obj.status if isinstance(round_obj.status, str) else round_obj.status.value,
                        "retry_count": round_obj.retry_count,
                        "error_message": round_obj.error_message,
                        "token_usage": round_obj.token_usage,
                        "model_config": round_obj.model_config
                    }
                    session_data["rounds"].append(round_data)

                return generate_execution_flow_from_session(session_data, task_id)
            else:
                print(f"❌ 未找到会话数据: {session_id}")
                # 尝试查找所有可用的会话
                try:
                    all_sessions = conversation_logger.search_sessions()
                    print(f"📋 最近的会话列表: {[s.get('session_id', 'unknown') for s in all_sessions[:10]]}")
                except Exception as list_error:
                    print(f"❌ 获取会话列表失败: {list_error}")

        except Exception as e:
            print(f"❌ 从会话数据生成执行链路失败: {e}")
            import traceback
            traceback.print_exc()

    # 如果没有找到真实数据，返回错误信息
    print(f"❌ 未找到真实数据，拒绝使用模拟数据: {task_id}")

    return {
        "task_id": task_id,
        "task_type": "data_not_found",
        "task_title": f"未找到任务数据: {task_id}",
        "started_at": "",
        "ended_at": "",
        "total_duration": "0s",
        "steps": [
            {
                "id": 1,
                "name": "数据查找失败",
                "type": "automated",
                "status": "failed",
                "description": f"未找到任务 {task_id} 的执行数据",
                "input": f"task_id: {task_id}",
                "output": "无数据",
                "duration": "0.1s",
                "details": {
                    "search_locations": ["会话日志", "任务记录"],
                    "api_key_required": False,
                    "local_processing": True
                }
            }
        ],
        "summary": {
            "total_steps": 1,
            "ai_steps": 0,
            "automated_steps": 1,
            "fallback_steps": 0,
            "success_rate": "0%",
            "ai_dependency": "无",
            "main_bottleneck": "数据缺失",
            "key_insight": "未找到对应的真实执行数据"
        }
    }


if __name__ == "__main__":
    # 创建必要的目录
    os.makedirs("web_ui/templates", exist_ok=True)
    os.makedirs("web_ui/static/css", exist_ok=True)
    os.makedirs("web_ui/static/js", exist_ok=True)

    print("🌐 启动对话分析Web界面...")
    print("📊 访问地址: http://localhost:8080")
    print("🔍 功能:")
    print("  - 会话列表和搜索")
    print("  - 详细对话查看")
    print("  - 统计分析图表")
    print("  - 实时数据更新")

    uvicorn.run(
        "conversation_viewer_app:app",
        host="0.0.0.0",
        port=8080,
        reload=True,
        log_level="info"
    )
