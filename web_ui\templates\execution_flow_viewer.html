{% extends "base.html" %}

{% block title %}执行链路可视化 - 对话分析系统{% endblock %}

{% block extra_css %}
<style>

    .execution-flow {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .step-card {
        border: 2px solid #ddd;
        border-radius: 8px;
        padding: 15px;
        position: relative;
        transition: all 0.3s ease;
        background: white;
    }

    .step-card:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

        /* 不同类型的步骤样式 */
        .step-ai {
            border-color: #ff6b6b;
            background: linear-gradient(135deg, #fff5f5 0%, #ffe0e0 100%);
        }

        .step-automated {
            border-color: #4ecdc4;
            background: linear-gradient(135deg, #f0fffe 0%, #e0f7f6 100%);
        }

        .step-fallback {
            border-color: #ffa726;
            background: linear-gradient(135deg, #fff8f0 0%, #ffecdb 100%);
        }

        .step-failed {
            border-color: #f44336;
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
        }

        .step-success {
            border-color: #4caf50;
            background: linear-gradient(135deg, #f1f8e9 0%, #dcedc8 100%);
        }

        .step-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .step-title {
            font-weight: bold;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .step-type-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }

        .badge-ai {
            background-color: #ff6b6b;
        }

        .badge-automated {
            background-color: #4ecdc4;
        }

        .badge-fallback {
            background-color: #ffa726;
        }

        .step-status {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        .status-success {
            background-color: #4caf50;
            color: white;
        }

        .status-failed {
            background-color: #f44336;
            color: white;
        }

        .status-running {
            background-color: #2196f3;
            color: white;
        }

        .step-details {
            margin-top: 10px;
        }

        .detail-section {
            margin-bottom: 10px;
        }

        .detail-label {
            font-weight: bold;
            color: #666;
            margin-bottom: 5px;
        }

        .detail-content {
            background-color: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }

        .ai-prompt {
            border-left-color: #ff6b6b;
        }

        .ai-response {
            border-left-color: #4ecdc4;
        }

        .error-content {
            border-left-color: #f44336;
            background-color: #ffebee;
        }

        .toggle-button {
            background: none;
            border: 1px solid #ddd;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .toggle-button:hover {
            background-color: #f0f0f0;
        }

        .collapsible {
            display: none;
        }

        .collapsible.show {
            display: block;
        }

        .flow-arrow {
            text-align: center;
            color: #666;
            font-size: 20px;
            margin: 5px 0;
        }

        .legend {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }
    .stat-label {
        color: #666;
        font-size: 14px;
        margin-top: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-project-diagram text-primary"></i>
        执行链路可视化
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <select id="taskSelect" class="form-select form-select-sm">
                <option value="">正在加载任务列表...</option>
            </select>
        </div>
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-primary" onclick="loadTaskFlow()">
                <i class="fas fa-sync-alt"></i> 加载执行链路
            </button>
        </div>
    </div>
</div>

<div class="alert alert-info">
    <i class="fas fa-info-circle"></i>
    <strong>功能说明：</strong>
    清晰展示任务执行过程中的每个步骤，区分AI接口调用和自动化处理。
    <span class="badge bg-danger ms-2">🤖 AI接口</span>
    <span class="badge bg-success ms-2">⚙️ 自动化</span>
    <span class="badge bg-warning ms-2">🔄 Fallback</span>
</div>

<!-- 统计摘要 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary" id="total-steps">0</h5>
                <p class="card-text text-muted">总步骤数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-danger" id="ai-steps">0</h5>
                <p class="card-text text-muted">AI接口调用</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success" id="automated-steps">0</h5>
                <p class="card-text text-muted">自动化处理</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning" id="fallback-steps">0</h5>
                <p class="card-text text-muted">Fallback策略</p>
            </div>
        </div>
    </div>
</div>

<!-- 执行流程 -->
<div class="execution-flow" id="execution-flow">
    <div class="text-center text-muted py-5">
        <i class="fas fa-project-diagram fa-3x mb-3"></i>
        <div>选择任务并点击"加载执行链路"查看详细流程</div>
    </div>
</div>

{% endblock %}

{% block extra_js %}

    <script>
        // 示例数据 - 实际使用时从后端获取
        const executionData = {
            task_id: "task_1748505049_1748505049",
            task_type: "job_failure_analysis",
            steps: [
                {
                    id: 1,
                    name: "获取作业日志",
                    type: "automated",
                    status: "success",
                    description: "从GitLab API获取Job 803的完整日志",
                    input: "job_id: 803, project_id: 3",
                    output: "成功获取2.3KB日志内容",
                    duration: "0.5s",
                    details: {
                        method: "GitLab API调用",
                        api_endpoint: "/api/v4/projects/3/jobs/803/trace",
                        response_size: "2.3KB"
                    }
                },
                {
                    id: 2,
                    name: "关键词匹配错误分析",
                    type: "automated",
                    status: "success",
                    description: "使用预定义模式匹配分析错误类型",
                    input: "原始日志内容",
                    output: "识别为: build_error (通用构建错误)",
                    duration: "0.1s",
                    details: {
                        patterns_matched: ["Job failed: exit code 1"],
                        error_type: "build_error",
                        confidence: "低 (通用模式)"
                    }
                },
                {
                    id: 3,
                    name: "AI智能错误分析",
                    type: "ai",
                    status: "failed",
                    description: "使用AI深度分析原始日志内容",
                    input: "完整原始日志 + 项目上下文",
                    output: "失败: OPENROUTER_API_KEY未设置",
                    duration: "15.2s (重试3次)",
                    error: "OPENROUTER_API_KEY环境变量未设置",
                    details: {
                        model: "deepseek/deepseek-r1:free",
                        retry_attempts: 3,
                        failure_reason: "API key missing"
                    }
                },
                {
                    id: 4,
                    name: "Fallback错误分析",
                    type: "fallback",
                    status: "success",
                    description: "AI失败后使用智能fallback策略",
                    input: "关键词匹配结果 + 原始日志片段",
                    output: "识别为: 网络超时错误",
                    duration: "0.3s",
                    details: {
                        fallback_method: "原始日志模式匹配",
                        detected_patterns: ["socket.timeout", "ReadTimeoutError"],
                        corrected_error_type: "network_timeout"
                    }
                },
                {
                    id: 5,
                    name: "生成修复方案",
                    type: "automated",
                    status: "success",
                    description: "基于错误类型生成针对性修复步骤",
                    input: "error_type: network_timeout",
                    output: "生成2个修复步骤",
                    duration: "0.1s",
                    details: {
                        fix_steps: [
                            "使用国内镜像源重试pip安装",
                            "如果镜像源失败，尝试阿里云镜像"
                        ]
                    }
                }
            ]
        };

        function renderExecutionFlow(data) {
            const flowContainer = document.getElementById('execution-flow');
            const steps = data.steps;

            // 更新统计
            updateStats(steps);

            // 清空容器
            flowContainer.innerHTML = '';

            steps.forEach((step, index) => {
                // 创建步骤卡片
                const stepCard = createStepCard(step);
                flowContainer.appendChild(stepCard);

                // 添加箭头（除了最后一个步骤）
                if (index < steps.length - 1) {
                    const arrow = document.createElement('div');
                    arrow.className = 'flow-arrow';
                    arrow.innerHTML = '↓';
                    flowContainer.appendChild(arrow);
                }
            });
        }

        function createStepCard(step) {
            const card = document.createElement('div');
            card.className = `card mb-3 step-${step.type} step-${step.status}`;

            // 根据类型设置边框颜色
            let borderClass = '';
            if (step.type === 'ai') borderClass = 'border-danger';
            else if (step.type === 'automated') borderClass = 'border-success';
            else if (step.type === 'fallback') borderClass = 'border-warning';

            card.className += ` ${borderClass}`;

            card.innerHTML = `
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <span class="fw-bold me-2">${step.id}. ${step.name}</span>
                        <span class="badge ${step.type === 'ai' ? 'bg-danger' : step.type === 'automated' ? 'bg-success' : 'bg-warning'}">
                            ${getTypeLabel(step.type)}
                        </span>
                    </div>
                    <span class="badge ${step.status === 'success' ? 'bg-success' : step.status === 'failed' ? 'bg-danger' : 'bg-primary'}">
                        ${getStatusLabel(step.status)}
                    </span>
                </div>

                <div class="card-body">
                    <p class="card-text">${step.description}</p>

                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">输入:</h6>
                            <div class="bg-light p-2 rounded small">${step.input}</div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">输出:</h6>
                            <div class="bg-light p-2 rounded small ${step.status === 'failed' ? 'bg-danger-subtle' : ''}">${step.output}</div>
                        </div>
                    </div>

                    ${step.error ? `
                    <div class="mt-3">
                        <h6 class="text-danger">错误信息:</h6>
                        <div class="bg-danger-subtle p-2 rounded small">${step.error}</div>
                    </div>
                    ` : ''}

                    <!-- 直接显示重要的技术详情 -->
                    <div class="mt-3">
                        <div class="row">
                            <div class="col-md-4">
                                <h6 class="text-muted mb-1">执行时间:</h6>
                                <span class="badge bg-info">${step.duration}</span>
                            </div>
                            <div class="col-md-4">
                                <h6 class="text-muted mb-1">API密钥:</h6>
                                <span class="badge ${step.details.api_key_required ? 'bg-warning' : 'bg-success'}">${step.details.api_key_required ? '需要' : '不需要'}</span>
                            </div>
                            <div class="col-md-4">
                                <h6 class="text-muted mb-1">处理方式:</h6>
                                <span class="badge ${step.details.local_processing ? 'bg-secondary' : 'bg-primary'}">${step.details.local_processing ? '本地处理' : '远程API'}</span>
                            </div>
                        </div>
                    </div>

                    <!-- AI接口调用的详细信息 -->
                    ${step.type === 'ai' ? `
                    <div class="mt-3">
                        <h6 class="text-primary">🤖 AI接口详情:</h6>
                        <div class="bg-primary-subtle p-3 rounded">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>模型名称:</strong> <code>${step.details.model_name || '未知'}</code><br>
                                    <strong>API提供商:</strong> ${step.details.api_provider || '未知'}<br>
                                    <strong>重试次数:</strong> ${step.details.retry_count || 0}
                                </div>
                                <div class="col-md-6">
                                    ${step.details.token_usage && Object.keys(step.details.token_usage).length > 0 ? `
                                    <strong>Token使用:</strong><br>
                                    <small>
                                        输入: ${step.details.token_usage.prompt_tokens || 'N/A'}<br>
                                        输出: ${step.details.token_usage.completion_tokens || 'N/A'}<br>
                                        总计: ${step.details.token_usage.total_tokens || 'N/A'}
                                    </small>
                                    ` : '<strong>Token使用:</strong> 无记录'}
                                </div>
                            </div>
                            ${step.details.prompt_preview ? `
                            <div class="mt-2">
                                <strong>提示词内容:</strong>
                                <div class="bg-white border p-2 rounded mt-1 small" style="max-height: 150px; overflow-y: auto; white-space: pre-wrap; font-family: monospace;">
${step.details.prompt_preview}
                                </div>
                                <div class="mt-1">
                                    <button class="btn btn-xs btn-outline-primary" onclick="copyToClipboard('${step.id}_prompt', '${step.details.prompt_preview.replace(/'/g, "\\'")}')">
                                        <i class="fas fa-copy"></i> 复制提示词
                                    </button>
                                </div>
                            </div>
                            ` : ''}
                            ${step.details.response_preview ? `
                            <div class="mt-2">
                                <strong>AI响应内容:</strong>
                                <div class="bg-white border p-2 rounded mt-1 small" style="max-height: 150px; overflow-y: auto; white-space: pre-wrap; font-family: monospace;">
${step.details.response_preview}
                                </div>
                                <div class="mt-1">
                                    <button class="btn btn-xs btn-outline-success" onclick="copyToClipboard('${step.id}_response', '${step.details.response_preview.replace(/'/g, "\\'")}')">
                                        <i class="fas fa-copy"></i> 复制响应
                                    </button>
                                </div>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                    ` : ''}

                    <!-- 系统处理器的详细信息 -->
                    ${step.type === 'automated' && step.details.processor_name ? `
                    <div class="mt-3">
                        <h6 class="text-success">⚙️ 系统处理器详情:</h6>
                        <div class="bg-success-subtle p-3 rounded">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>处理器名称:</strong> <code>${step.details.processor_name}</code><br>
                                    <strong>处理器类型:</strong> ${step.details.processor_type || '未知'}<br>
                                    <strong>处理方法:</strong> ${step.details.processing_method || '未知'}
                                </div>
                                <div class="col-md-6">
                                    <strong>重试次数:</strong> ${step.details.retry_count || 0}<br>
                                    <strong>本地处理:</strong> ${step.details.local_processing ? '是' : '否'}
                                </div>
                            </div>
                            ${step.details.input_preview ? `
                            <div class="mt-2">
                                <strong>输入预览:</strong>
                                <div class="bg-light p-2 rounded mt-1 small" style="max-height: 100px; overflow-y: auto;">
                                    ${step.details.input_preview}
                                </div>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                    ` : ''}

                    <!-- 可折叠的完整技术详情 -->
                    <div class="mt-3 text-end">
                        <button class="btn btn-sm btn-outline-secondary" onclick="toggleDetails(${step.id})">
                            <i class="fas fa-list"></i> 查看完整详情
                        </button>
                    </div>

                    <div id="details-${step.id}" class="collapse mt-3">
                        <div class="bg-light p-3 rounded">
                            <h6 class="mb-3">📋 完整技术详情</h6>

                            <!-- 基本信息表格 -->
                            <div class="table-responsive mb-3">
                                <table class="table table-sm table-bordered">
                                    <tbody>
                                        ${Object.entries(step.details).map(([key, value]) => {
                                            // 跳过预览字段，因为已经单独显示了
                                            if (key.includes('preview')) return '';

                                            let displayValue = value;
                                            if (typeof value === 'object' && value !== null) {
                                                if (key === 'token_usage' && Object.keys(value).length > 0) {
                                                    displayValue = Object.entries(value).map(([k, v]) => `${k}: ${v}`).join(', ');
                                                } else {
                                                    displayValue = JSON.stringify(value, null, 2);
                                                }
                                            } else if (typeof value === 'boolean') {
                                                displayValue = value ? '是' : '否';
                                            }

                                            return `
                                            <tr>
                                                <td class="fw-bold" style="width: 30%;">${key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</td>
                                                <td style="word-break: break-all;">${displayValue || '无'}</td>
                                            </tr>
                                            `;
                                        }).join('')}
                                    </tbody>
                                </table>
                            </div>

                            <!-- 长文本内容单独显示 -->
                            ${step.details.prompt_preview ? `
                            <div class="mb-3">
                                <h6 class="text-primary">📝 完整提示词内容:</h6>
                                <div class="bg-white border p-3 rounded" style="max-height: 200px; overflow-y: auto; white-space: pre-wrap; font-family: monospace; font-size: 12px;">
${step.details.prompt_preview}
                                </div>
                            </div>
                            ` : ''}

                            ${step.details.response_preview ? `
                            <div class="mb-3">
                                <h6 class="text-success">💬 完整响应内容:</h6>
                                <div class="bg-white border p-3 rounded" style="max-height: 200px; overflow-y: auto; white-space: pre-wrap; font-family: monospace; font-size: 12px;">
${step.details.response_preview}
                                </div>
                            </div>
                            ` : ''}

                            ${step.details.input_preview ? `
                            <div class="mb-3">
                                <h6 class="text-info">📥 完整输入内容:</h6>
                                <div class="bg-white border p-3 rounded" style="max-height: 200px; overflow-y: auto; white-space: pre-wrap; font-family: monospace; font-size: 12px;">
${step.details.input_preview}
                                </div>
                            </div>
                            ` : ''}

                            ${step.details.output_preview ? `
                            <div class="mb-3">
                                <h6 class="text-warning">📤 完整输出内容:</h6>
                                <div class="bg-white border p-3 rounded" style="max-height: 200px; overflow-y: auto; white-space: pre-wrap; font-family: monospace; font-size: 12px;">
${step.details.output_preview}
                                </div>
                            </div>
                            ` : ''}

                            <!-- 原始JSON数据（可选） -->
                            <details class="mt-3">
                                <summary class="btn btn-sm btn-outline-dark">🔧 查看原始JSON数据</summary>
                                <pre class="small mt-2 bg-dark text-light p-3 rounded" style="max-height: 300px; overflow-y: auto;">${JSON.stringify(step.details, null, 2)}</pre>
                            </details>
                        </div>
                    </div>
                </div>
            `;

            return card;
        }

        function getTypeLabel(type) {
            const labels = {
                'ai': '🤖 AI接口',
                'automated': '⚙️ 自动化',
                'fallback': '🔄 Fallback'
            };
            return labels[type] || type;
        }

        function getStatusLabel(status) {
            const labels = {
                'success': '✅ 成功',
                'failed': '❌ 失败',
                'running': '🔄 运行中'
            };
            return labels[status] || status;
        }

        function updateStats(steps) {
            document.getElementById('total-steps').textContent = steps.length;
            document.getElementById('ai-steps').textContent = steps.filter(s => s.type === 'ai').length;
            document.getElementById('automated-steps').textContent = steps.filter(s => s.type === 'automated').length;
            document.getElementById('fallback-steps').textContent = steps.filter(s => s.type === 'fallback').length;
        }

        function toggleDetails(stepId) {
            const details = document.getElementById(`details-${stepId}`);
            const bsCollapse = new bootstrap.Collapse(details, {
                toggle: true
            });
        }

        // 从API加载任务执行链路
        async function loadTaskFlow() {
            const taskId = document.getElementById('taskSelect').value;

            try {
                // 显示加载状态
                const flowContainer = document.getElementById('execution-flow');
                flowContainer.innerHTML = '<div class="text-center text-muted py-5"><i class="fas fa-spinner fa-spin fa-3x mb-3"></i><div>正在加载执行链路...</div></div>';

                // 从API获取数据
                const response = await fetch(`/api/execution-flow/${taskId}`);
                const result = await response.json();

                if (result.success) {
                    renderExecutionFlow(result.data);

                    // 显示调试信息
                    if (result.debug_info) {
                        console.log('🔍 执行链路调试信息:', result.debug_info);

                        // 在页面上显示数据源信息
                        const debugBadge = document.createElement('div');
                        debugBadge.className = 'alert alert-info mt-3';
                        debugBadge.innerHTML = `
                            <i class="fas fa-info-circle"></i>
                            <strong>数据源:</strong> ${result.debug_info.data_source === 'real_session' ? '真实会话数据' : '模拟数据'}
                            <span class="ms-3"><strong>任务ID:</strong> ${result.debug_info.task_id}</span>
                            <span class="ms-3"><strong>生成时间:</strong> ${new Date(result.debug_info.timestamp).toLocaleString()}</span>
                        `;
                        flowContainer.insertBefore(debugBadge, flowContainer.firstChild);
                    }
                } else {
                    let errorHtml = `<div class="text-center text-danger py-5"><i class="fas fa-exclamation-triangle fa-3x mb-3"></i><div>加载失败: ${result.error}</div>`;

                    // 显示调试信息
                    if (result.debug_info) {
                        console.error('❌ 执行链路错误信息:', result.debug_info);
                        errorHtml += `<div class="mt-3 text-muted small">调试信息: ${JSON.stringify(result.debug_info)}</div>`;
                    }

                    errorHtml += '</div>';
                    flowContainer.innerHTML = errorHtml;
                }
            } catch (error) {
                console.error('加载执行链路失败:', error);
                const flowContainer = document.getElementById('execution-flow');
                flowContainer.innerHTML = `<div class="text-center text-danger py-5"><i class="fas fa-exclamation-triangle fa-3x mb-3"></i><div>网络错误: ${error.message}</div></div>`;
            }
        }

        // 从URL参数获取任务ID
        function getTaskIdFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            const taskId = urlParams.get('task_id');
            const sessionId = urlParams.get('session_id');

            if (taskId) {
                return taskId;
            } else if (sessionId) {
                // 如果只有session_id，生成对应的task_id
                return `task_${sessionId}`;
            }

            // 默认返回示例任务
            return 'task_1748505049_1748505049';
        }

        // 加载可用任务列表
        async function loadAvailableTasks() {
            const taskSelect = document.getElementById('taskSelect');

            try {
                const response = await fetch('/api/available-tasks');
                const result = await response.json();

                // 清空现有选项
                taskSelect.innerHTML = '';

                if (result.success && result.data.length > 0) {
                    // 添加任务选项
                    result.data.forEach(task => {
                        const option = document.createElement('option');
                        option.value = task.task_id;
                        option.text = task.display_name;
                        taskSelect.appendChild(option);
                    });

                    console.log(`✅ 加载了 ${result.data.length} 个可用任务`);
                } else {
                    // 没有可用任务
                    const option = document.createElement('option');
                    option.value = '';
                    option.text = '暂无可用任务';
                    option.disabled = true;
                    taskSelect.appendChild(option);

                    console.log('⚠️ 没有找到可用任务');
                }

                return result.data || [];
            } catch (error) {
                console.error('❌ 加载任务列表失败:', error);

                // 显示错误状态
                taskSelect.innerHTML = '';
                const option = document.createElement('option');
                option.value = '';
                option.text = '加载失败，请刷新重试';
                option.disabled = true;
                taskSelect.appendChild(option);

                return [];
            }
        }

        // 复制到剪贴板功能
        function copyToClipboard(elementId, text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(function() {
                    // 显示成功提示
                    const button = event.target.closest('button');
                    const originalText = button.innerHTML;
                    button.innerHTML = '<i class="fas fa-check"></i> 已复制';
                    button.classList.add('btn-success');

                    setTimeout(() => {
                        button.innerHTML = originalText;
                        button.classList.remove('btn-success');
                    }, 2000);
                }).catch(function(err) {
                    console.error('复制失败:', err);
                    alert('复制失败，请手动选择文本复制');
                });
            } else {
                // 降级方案：选择文本
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                try {
                    document.execCommand('copy');
                    alert('内容已复制到剪贴板');
                } catch (err) {
                    alert('复制失败，请手动选择文本复制');
                }
                document.body.removeChild(textArea);
            }
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', async function() {
            // 先加载可用任务列表
            const availableTasks = await loadAvailableTasks();

            // 从URL参数获取任务ID
            const urlTaskId = getTaskIdFromUrl();
            const taskSelect = document.getElementById('taskSelect');

            // 如果URL中指定了任务ID，尝试选中它
            if (urlTaskId && urlTaskId !== 'task_1748505049_1748505049') {
                // 检查任务ID是否在列表中
                const existingOptions = Array.from(taskSelect.options).map(opt => opt.value);
                if (!existingOptions.includes(urlTaskId)) {
                    // 如果不在列表中，添加它
                    const option = document.createElement('option');
                    option.value = urlTaskId;
                    option.text = `来自链接的任务 (${urlTaskId})`;
                    option.selected = true;
                    taskSelect.appendChild(option);
                } else {
                    // 如果在列表中，选中它
                    taskSelect.value = urlTaskId;
                }

                // 自动加载任务
                loadTaskFlow();
            } else if (availableTasks.length > 0) {
                // 如果没有指定任务ID，选择第一个可用任务
                taskSelect.selectedIndex = 0;
                loadTaskFlow();
            }
        });
    </script>
{% endblock %}
