{% extends "base.html" %}

{% block title %}实时监控 - Aider行为分析系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2 fw-bold">
        <i class="fas fa-chart-line text-primary"></i>
        Aider实时监控
    </h1>
    <p class="text-muted mb-0">AI编程助手行为分析和性能监控</p>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshData()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="showClearLogsModal()">
                <i class="fas fa-trash-alt"></i> 清空日志
            </button>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4" id="statsCards">
    <div class="col-md-3 mb-3">
        <div class="card metric-card text-white" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="metric-value" id="totalSessions">-</div>
            <div class="metric-label">执行任务</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card metric-card text-white" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
            <div class="metric-value" id="successRate">-</div>
            <div class="metric-label">成功率</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card metric-card text-white" style="background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);">
            <div class="metric-value" id="avgRounds">-</div>
            <div class="metric-label">平均交互</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card metric-card text-white" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
            <div class="metric-value" id="totalRounds">-</div>
            <div class="metric-label">总交互数</div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line"></i>
                    Aider执行趋势
                </h5>
            </div>
            <div class="card-body">
                <canvas id="dailyChart" height="150"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-pie-chart"></i>
                    任务类型分布
                </h5>
            </div>
            <div class="card-body">
                <canvas id="taskTypeChart" height="150"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 最近会话 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history"></i>
                    最近执行记录
                </h5>
                <a href="/sessions" class="btn btn-sm btn-outline-primary">
                    查看全部 <i class="fas fa-arrow-right"></i>
                </a>
            </div>
            <div class="card-body">
                <div id="recentSessions">
                    <div class="loading">
                        <div class="spinner"></div>
                        <div>加载最近会话...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 清空日志模态框 -->
<div class="modal fade" id="clearLogsModal" tabindex="-1" aria-labelledby="clearLogsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="clearLogsModalLabel">
                    <i class="fas fa-trash-alt text-danger"></i>
                    清空日志
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>警告：</strong>此操作将永久删除选定的日志文件，无法恢复！
                </div>

                <form id="clearLogsForm">
                    <div class="mb-3">
                        <label class="form-label">日志类型</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="logType" id="clearAll" value="all" checked>
                            <label class="form-check-label" for="clearAll">
                                <i class="fas fa-globe"></i> 全部日志
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="logType" id="clearConversations" value="conversations">
                            <label class="form-check-label" for="clearConversations">
                                <i class="fas fa-comments"></i> 对话日志
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="logType" id="clearErrors" value="errors">
                            <label class="form-check-label" for="clearErrors">
                                <i class="fas fa-exclamation-circle"></i> 错误日志
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="logType" id="clearSystem" value="system">
                            <label class="form-check-label" for="clearSystem">
                                <i class="fas fa-cog"></i> 系统日志
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="daysToKeep" class="form-label">保留天数</label>
                        <select class="form-select" id="daysToKeep" name="daysToKeep">
                            <option value="0">全部清空</option>
                            <option value="1">保留最近1天</option>
                            <option value="3">保留最近3天</option>
                            <option value="7">保留最近7天</option>
                            <option value="30">保留最近30天</option>
                        </select>
                        <div class="form-text">选择要保留的最近日志天数，其余将被删除</div>
                    </div>
                </form>

                <div id="clearLogsResult" class="mt-3" style="display: none;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="executeClearLogs()" id="clearLogsBtn">
                    <i class="fas fa-trash-alt"></i> 确认清空
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let dailyChart = null;
let taskTypeChart = null;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadDashboardData();
});

// 加载仪表板数据
async function loadDashboardData() {
    try {
        // 加载统计数据
        const statsResponse = await fetch('/api/statistics?days=7');
        const statsData = await statsResponse.json();

        if (statsData.success) {
            updateStatsCards(statsData.data);
            updateCharts(statsData.data);
        }

        // 加载最近会话
        const sessionsResponse = await fetch('/api/sessions?limit=10');
        const sessionsData = await sessionsResponse.json();

        if (sessionsData.success) {
            updateRecentSessions(sessionsData.data);
        }

    } catch (error) {
        console.error('加载仪表板数据失败:', error);
        showError('recentSessions', '加载数据失败，请稍后重试');
    }
}

// 更新统计卡片
function updateStatsCards(data) {
    const summary = data.summary;

    document.getElementById('totalSessions').textContent = summary.total_sessions;
    document.getElementById('successRate').textContent = `${summary.success_rate.toFixed(1)}%`;
    document.getElementById('avgRounds').textContent = summary.avg_rounds;
    document.getElementById('totalRounds').textContent = summary.total_rounds;
}

// 更新图表
function updateCharts(data) {
    updateDailyChart(data.daily_stats);
    updateTaskTypeChart(data.task_types);
}

// 更新每日趋势图表
function updateDailyChart(dailyStats) {
    const ctx = document.getElementById('dailyChart').getContext('2d');

    // 准备数据
    const dates = Object.keys(dailyStats).sort();
    const totalData = dates.map(date => dailyStats[date].total);
    const successData = dates.map(date => dailyStats[date].success);
    const failedData = dates.map(date => dailyStats[date].failed);

    if (dailyChart) {
        dailyChart.destroy();
    }

    dailyChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: dates,
            datasets: [
                {
                    label: '总数',
                    data: totalData,
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4
                },
                {
                    label: '成功',
                    data: successData,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                },
                {
                    label: '失败',
                    data: failedData,
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                }
            }
        }
    });
}

// 更新任务类型饼图
function updateTaskTypeChart(taskTypes) {
    const ctx = document.getElementById('taskTypeChart').getContext('2d');

    // 准备数据
    const labels = Object.keys(taskTypes);
    const data = labels.map(type => taskTypes[type].total);
    const colors = [
        '#007bff', '#28a745', '#dc3545', '#ffc107',
        '#17a2b8', '#6f42c1', '#e83e8c', '#fd7e14'
    ];

    if (taskTypeChart) {
        taskTypeChart.destroy();
    }

    taskTypeChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors.slice(0, labels.length),
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// 更新最近会话列表
function updateRecentSessions(sessions) {
    const container = document.getElementById('recentSessions');

    if (sessions.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-inbox fa-3x mb-3"></i>
                <div>暂无会话记录</div>
            </div>
        `;
        return;
    }

    const html = sessions.map(session => `
        <div class="session-item p-3 border-bottom" onclick="viewSession('${session.session_id}')">
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <h6 class="mb-1">${session.task_title}</h6>
                    <p class="mb-1 text-muted small">${session.task_type}</p>
                    <small class="text-muted">
                        <i class="fas fa-clock"></i>
                        ${formatTime(session.started_at)}
                        <span class="ms-2">
                            <i class="fas fa-comments"></i>
                            ${session.rounds_count} 轮
                        </span>
                    </small>
                </div>
                <div class="text-end">
                    ${getStatusBadge(session.status)}
                </div>
            </div>
        </div>
    `).join('');

    container.innerHTML = html;
}

// 查看会话详情
function viewSession(sessionId) {
    window.location.href = `/sessions/${sessionId}`;
}

// 刷新数据
function refreshData() {
    showLoading('recentSessions');
    loadDashboardData();
}

// 显示清空日志模态框
function showClearLogsModal() {
    const modal = new bootstrap.Modal(document.getElementById('clearLogsModal'));
    modal.show();

    // 重置表单
    document.getElementById('clearLogsForm').reset();
    document.getElementById('clearLogsResult').style.display = 'none';
    document.getElementById('clearLogsBtn').disabled = false;
}

// 执行清空日志
async function executeClearLogs() {
    const form = document.getElementById('clearLogsForm');
    const formData = new FormData(form);
    const logType = formData.get('logType');
    const daysToKeep = parseInt(formData.get('daysToKeep'));

    const resultDiv = document.getElementById('clearLogsResult');
    const clearBtn = document.getElementById('clearLogsBtn');

    // 禁用按钮，显示加载状态
    clearBtn.disabled = true;
    clearBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 清空中...';

    try {
        const response = await fetch('/api/logs/clear', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                log_type: logType,
                days_to_keep: daysToKeep
            })
        });

        const result = await response.json();

        // 显示结果
        resultDiv.style.display = 'block';

        if (result.success) {
            resultDiv.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <strong>清空成功！</strong><br>
                    ${result.message}

                    ${result.details ? `
                        <div class="mt-2">
                            <small>
                                ${result.details.conversations ? `对话日志: ${result.details.conversations.cleared_count || 0} 个文件<br>` : ''}
                                ${result.details.errors ? `错误日志: ${result.details.errors.cleared_count || 0} 个文件<br>` : ''}
                                ${result.details.system ? `系统日志: ${result.details.system.cleared_count || 0} 个文件<br>` : ''}
                            </small>
                        </div>
                    ` : ''}
                </div>
            `;

            // 刷新页面数据
            setTimeout(() => {
                refreshData();
            }, 2000);

        } else {
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i>
                    <strong>清空失败！</strong><br>
                    ${result.error}
                </div>
            `;
        }

    } catch (error) {
        console.error('清空日志失败:', error);
        resultDiv.style.display = 'block';
        resultDiv.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i>
                <strong>清空失败！</strong><br>
                网络错误或服务器异常，请稍后重试。
            </div>
        `;
    } finally {
        // 恢复按钮状态
        clearBtn.disabled = false;
        clearBtn.innerHTML = '<i class="fas fa-trash-alt"></i> 确认清空';
    }
}
</script>
{% endblock %}
