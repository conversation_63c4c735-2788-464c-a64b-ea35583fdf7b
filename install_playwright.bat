@echo off
echo 🚀 开始安装Playwright...

REM 激活虚拟环境
echo 🔄 激活虚拟环境...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ❌ 虚拟环境激活失败
    pause
    exit /b 1
)

REM 安装Playwright包
echo 🔄 安装Playwright Python包...
pip install playwright==1.51.0
if errorlevel 1 (
    echo ❌ Playwright包安装失败
    pause
    exit /b 1
)

REM 安装Chromium浏览器
echo 🔄 安装Chromium浏览器和依赖...
python -m playwright install --with-deps chromium
if errorlevel 1 (
    echo ❌ Chromium浏览器安装失败
    pause
    exit /b 1
)

REM 验证安装
echo 🔍 验证安装...
python -c "import playwright; print(f'✅ Playwright版本: {playwright.__version__}')"
if errorlevel 1 (
    echo ❌ Playwright验证失败
    pause
    exit /b 1
)

echo 🎉 Playwright安装完成！
echo 现在可以在Aider中使用网页抓取功能了
pause
