#!/usr/bin/env python3
"""
Playwright自动安装脚本
自动安装Playwright Python包和Chromium浏览器
"""

import subprocess
import sys
import os
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_command(command, description):
    """执行命令并处理错误"""
    logger.info(f"🔄 {description}")
    logger.info(f"执行命令: {command}")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=True, 
            capture_output=True, 
            text=True,
            encoding='utf-8'
        )
        logger.info(f"✅ {description} - 成功")
        if result.stdout:
            logger.info(f"输出: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ {description} - 失败")
        logger.error(f"错误码: {e.returncode}")
        if e.stdout:
            logger.error(f"标准输出: {e.stdout}")
        if e.stderr:
            logger.error(f"错误输出: {e.stderr}")
        return False
    except Exception as e:
        logger.error(f"❌ {description} - 异常: {e}")
        return False

def check_python():
    """检查Python环境"""
    logger.info("🔍 检查Python环境...")
    python_version = sys.version_info
    logger.info(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        logger.error("❌ Playwright需要Python 3.8+")
        return False
    
    logger.info("✅ Python版本符合要求")
    return True

def install_playwright_package():
    """安装Playwright Python包"""
    return run_command(
        "pip install playwright==1.51.0",
        "安装Playwright Python包"
    )

def install_chromium_browser():
    """安装Chromium浏览器"""
    return run_command(
        "python -m playwright install --with-deps chromium",
        "安装Chromium浏览器和依赖"
    )

def verify_installation():
    """验证安装"""
    logger.info("🔍 验证Playwright安装...")
    
    try:
        # 测试导入
        import playwright
        logger.info(f"✅ Playwright包导入成功，版本: {playwright.__version__}")
        
        # 测试浏览器
        result = subprocess.run(
            "python -c \"from playwright.sync_api import sync_playwright; p = sync_playwright().start(); browser = p.chromium.launch(); print('浏览器启动成功'); browser.close(); p.stop()\"",
            shell=True,
            check=True,
            capture_output=True,
            text=True,
            timeout=30
        )
        logger.info("✅ Chromium浏览器测试成功")
        return True
        
    except ImportError as e:
        logger.error(f"❌ Playwright包导入失败: {e}")
        return False
    except subprocess.TimeoutExpired:
        logger.error("❌ 浏览器测试超时")
        return False
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ 浏览器测试失败: {e.stderr}")
        return False
    except Exception as e:
        logger.error(f"❌ 验证过程异常: {e}")
        return False

def main():
    """主安装流程"""
    logger.info("🚀 开始安装Playwright...")
    
    # 检查Python环境
    if not check_python():
        sys.exit(1)
    
    # 安装Playwright包
    if not install_playwright_package():
        logger.error("❌ Playwright包安装失败")
        sys.exit(1)
    
    # 安装浏览器
    if not install_chromium_browser():
        logger.error("❌ Chromium浏览器安装失败")
        sys.exit(1)
    
    # 验证安装
    if not verify_installation():
        logger.error("❌ 安装验证失败")
        sys.exit(1)
    
    logger.info("🎉 Playwright安装完成！")
    logger.info("现在可以在Aider中使用网页抓取功能了")

if __name__ == "__main__":
    main()
