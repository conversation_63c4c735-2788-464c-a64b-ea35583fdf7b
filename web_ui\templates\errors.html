{% extends "base.html" %}

{% block title %}错误日志监控 - Aider行为分析系统{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism.min.css" rel="stylesheet">
<style>
    .error-item {
        cursor: pointer;
        transition: all 0.3s;
        border-left: 4px solid #dc3545;
    }
    .error-item:hover {
        background: #f8f9fa;
        border-radius: 8px;
        transform: translateX(4px);
    }
    .error-level-critical { border-left-color: #dc3545; }
    .error-level-error { border-left-color: #fd7e14; }
    .error-level-warning { border-left-color: #ffc107; }
    .error-level-info { border-left-color: #0dcaf0; }

    .traceback-container {
        background: #f8f9fa;
        border-radius: 4px;
        max-height: 300px;
        overflow-y: auto;
    }

    /* 彻底修复模态框层级问题 */

    /* 第一步：重置可能影响堆叠上下文的元素 */
    body.modal-open .content-wrapper {
        backdrop-filter: none !important;
        transform: none !important;
    }

    body.modal-open .main-content {
        backdrop-filter: none !important;
        transform: none !important;
    }

    body.modal-open .card {
        backdrop-filter: none !important;
        transform: none !important;
    }

    /* 第二步：设置极高的z-index值 */
    .modal {
        z-index: 999999 !important;
        position: fixed !important;
    }

    .modal-backdrop {
        z-index: 999998 !important;
        background-color: rgba(0, 0, 0, 0.5) !important;
        position: fixed !important;
    }

    .modal.show {
        display: block !important;
        z-index: 999999 !important;
    }

    .modal-dialog {
        z-index: 999999 !important;
        position: relative !important;
    }

    .modal-content {
        z-index: 999999 !important;
        position: relative !important;
        border: none;
        border-radius: 12px;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
        background: white !important;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        z-index: 999999 !important;
        position: relative !important;
        background: transparent !important;
    }

    /* 第三步：确保按钮和交互元素可用 */
    .modal .btn,
    .modal .btn-close,
    .modal input,
    .modal textarea,
    .modal select {
        z-index: 999999 !important;
        position: relative !important;
        pointer-events: auto !important;
    }

    /* 第四步：强制覆盖任何可能的冲突 */
    body.modal-open .modal-backdrop.show {
        z-index: 999998 !important;
        opacity: 0.5 !important;
    }

    body.modal-open .modal.show {
        z-index: 999999 !important;
    }

    /* 模态框内容样式 - 合并到上面的定义中 */

    .modal-header {
        border-bottom: 1px solid #e9ecef;
        border-radius: 12px 12px 0 0;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .modal-footer {
        border-top: 1px solid #e9ecef;
        border-radius: 0 0 12px 12px;
        background: #f8f9fa;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-exclamation-triangle text-danger"></i>
        错误日志监控
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" id="refreshBtn">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" id="exportBtn">
                <i class="fas fa-download"></i> 导出
            </button>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4" id="statsCards">
    <div class="col-md-3 mb-3">
        <div class="card metric-card text-white" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);">
            <div class="metric-value" id="totalErrors">-</div>
            <div class="metric-label">总错误数</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card metric-card text-white" style="background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);">
            <div class="metric-value" id="criticalErrors">-</div>
            <div class="metric-label">严重错误</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card metric-card text-white" style="background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);">
            <div class="metric-value" id="recentErrors">-</div>
            <div class="metric-label">最近1小时</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card metric-card text-white" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
            <div class="metric-value" id="errorRate">-</div>
            <div class="metric-label">错误率</div>
        </div>
    </div>
</div>

<!-- 搜索和过滤 -->
<div class="search-filters">
    <div class="row">
        <div class="col-md-2">
            <label for="hoursFilter" class="form-label">时间范围</label>
            <select class="form-select" id="hoursFilter">
                <option value="1">最近1小时</option>
                <option value="6">最近6小时</option>
                <option value="24" selected>最近24小时</option>
                <option value="72">最近3天</option>
                <option value="168">最近7天</option>
            </select>
        </div>
        <div class="col-md-2">
            <label for="levelFilter" class="form-label">错误级别</label>
            <select class="form-select" id="levelFilter">
                <option value="">全部级别</option>
                <option value="CRITICAL">严重</option>
                <option value="ERROR">错误</option>
                <option value="WARNING">警告</option>
            </select>
        </div>
        <div class="col-md-2">
            <label for="exceptionFilter" class="form-label">异常类型</label>
            <select class="form-select" id="exceptionFilter">
                <option value="">全部类型</option>
            </select>
        </div>
        <div class="col-md-2">
            <label for="loggerFilter" class="form-label">日志器</label>
            <select class="form-select" id="loggerFilter">
                <option value="">全部日志器</option>
            </select>
        </div>
        <div class="col-md-3">
            <label for="searchQuery" class="form-label">搜索</label>
            <input type="text" class="form-control" id="searchQuery" placeholder="搜索错误消息...">
        </div>
        <div class="col-md-1 d-flex align-items-end">
            <button type="button" class="btn btn-outline-secondary w-100" id="searchBtn">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>
</div>
<!-- 错误趋势图表 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line"></i>
                    错误趋势
                </h5>
            </div>
            <div class="card-body">
                <canvas id="errorTrendChart" height="100"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 错误列表 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-exclamation-triangle"></i>
            错误日志列表
        </h5>
        <span class="badge bg-secondary" id="errorCount">加载中...</span>
    </div>
    <div class="card-body p-0">
        <div id="errorsList">
            <div class="loading">
                <div class="spinner"></div>
                <div>正在收集错误日志...</div>
            </div>
        </div>
    </div>
</div>

<!-- 错误详情模态框 -->
<div class="modal fade" id="errorDetailModal" tabindex="-1" aria-labelledby="errorDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="errorDetailModalLabel">错误详情</h5>
                <button type="button" class="btn-close" onclick="closeModal()" aria-label="关闭"></button>
            </div>
            <div class="modal-body" id="errorDetailContent">
                <!-- 错误详情内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">
                    <i class="fas fa-times"></i> 关闭
                </button>
                <button type="button" class="btn btn-primary" id="copyErrorBtn" onclick="errorMonitor && errorMonitor.copyErrorToClipboard()">
                    <i class="fas fa-copy"></i> 复制错误信息
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
<script src="/static/js/errors.js"></script>
<script>


// 关闭模态框
function closeModal() {
    const modalElement = document.getElementById('errorDetailModal');

    // 尝试使用Bootstrap方式关闭
    const modalInstance = bootstrap.Modal.getInstance(modalElement);
    if (modalInstance) {
        modalInstance.hide();
    } else {
        // 强制关闭
        modalElement.style.display = 'none';
        modalElement.classList.remove('show');
        document.body.classList.remove('modal-open');

        // 移除背景遮罩
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());
    }

    console.log('模态框已关闭');
}



// 添加键盘快捷键支持
document.addEventListener('keydown', function(event) {
    // ESC键关闭模态框
    if (event.key === 'Escape') {
        closeModal();
    }
});
</script>
{% endblock %}
