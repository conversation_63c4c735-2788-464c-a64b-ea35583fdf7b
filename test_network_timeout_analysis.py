#!/usr/bin/env python3
"""
测试网络超时错误的分析 - 验证系统是否正确识别网络问题而不是代码问题
"""

import sys
import os
import asyncio
import traceback

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_network_timeout_analysis():
    """测试网络超时错误分析"""
    print("🌐 网络超时错误分析测试")
    print("=" * 50)
    
    try:
        from bot_agent.tools.intelligent_tool_coordinator import global_tool_coordinator
        
        # 模拟作业信息
        job_info = {
            'name': 'lint',
            'status': 'failed',
            'id': 927
        }
        
        # 模拟网络超时的作业日志
        job_log = """
[32;1m$ pip install black==23.3.0 flake8==6.0.0[0;m
Collecting black==23.3.0
  Downloading black-23.3.0-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.7 MB)
     âââââââââââââââââââââââââ¸                1.0/1.7 MB 36.0 kB/s eta 0:00:18
ERROR: Exception:
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/pip/_vendor/urllib3/response.py", line 438, in _error_catcher
    yield
  File "/usr/local/lib/python3.9/site-packages/pip/_vendor/urllib3/response.py", line 561, in read
    data = self._fp_read(amt) if not fp_closed else b""
  File "/usr/local/lib/python3.9/site-packages/pip/_vendor/urllib3/response.py", line 527, in _fp_read
    return self._fp.read(amt) if amt is not None else self._fp.read()
  File "/usr/local/lib/python3.9/site-packages/pip/_vendor/cachecontrol/filewrapper.py", line 90, in read
    data = self.__fp.read(amt)
  File "/usr/local/lib/python3.9/http/client.py", line 463, in read
    n = self.readinto(b)
  File "/usr/local/lib/python3.9/http/client.py", line 507, in readinto
    n = self.fp.readinto(b)
  File "/usr/local/lib/python3.9/socket.py", line 716, in readinto
    return self._sock.recv_into(b)
  File "/usr/local/lib/python3.9/ssl.py", line 1275, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/local/lib/python3.9/ssl.py", line 1133, in read
    return self._sslobj.read(len, buffer)
socket.timeout: The read operation timed out

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/pip/_internal/cli/base_command.py", line 160, in exc_logging_wrapper
    status = run_func(*args)
  File "/usr/local/lib/python3.9/site-packages/pip/_internal/cli/req_command.py", line 247, in wrapper
    return func(self, options, args)
  File "/usr/local/lib/python3.9/site-packages/pip/_internal/commands/install.py", line 419, in run
    requirement_set = resolver.resolve(
  File "/usr/local/lib/python3.9/site-packages/pip/_internal/resolution/resolvelib/resolver.py", line 92, in resolve
    result = self._result = resolver.resolve(
  File "/usr/local/lib/python3.9/site-packages/pip/_vendor/resolvelib/resolvers.py", line 481, in resolve
    state = resolution.resolve(requirements, max_rounds=max_rounds)
  File "/usr/local/lib/python3.9/site-packages/pip/_vendor/resolvelib/resolvers.py", line 348, in resolve
    self._add_to_criteria(self.state.criteria, r, parent=None)
  File "/usr/local/lib/python3.9/site-packages/pip/_vendor/resolvelib/resolvers.py", line 172, in _add_to_criteria
    if not criterion.candidates:
  File "/usr/local/lib/python3.9/site-packages/pip/_vendor/resolvelib/structs.py", line 151, in __bool__
    return bool(self._sequence)
  File "/usr/local/lib/python3.9/site-packages/pip/_internal/resolution/resolvelib/found_candidates.py", line 155, in __bool__
    return any(self)
  File "/usr/local/lib/python3.9/site-packages/pip/_internal/resolution/resolvelib/found_candidates.py", line 143, in <genexpr>
    return (c for c in iterator if id(c) not in self._incompatible_ids)
  File "/usr/local/lib/python3.9/site-packages/pip/_internal/resolution/resolvelib/found_candidates.py", line 47, in _iter_built
    candidate = func()
  File "/usr/local/lib/python3.9/site-packages/pip/_internal/resolution/resolvelib/factory.py", line 206, in _make_candidate_from_link
    self._link_candidate_cache[link] = LinkCandidate(
  File "/usr/local/lib/python3.9/site-packages/pip/_internal/resolution/resolvelib/candidates.py", line 297, in __init__
    super().__init__(
  File "/usr/local/lib/python3.9/site-packages/pip/_internal/resolution/resolvelib/candidates.py", line 162, in __init__
    self.dist = self._prepare()
  File "/usr/local/lib/python3.9/site-packages/pip/_internal/resolution/resolvelib/candidates.py", line 231, in _prepare
    dist = self._prepare_distribution()
  File "/usr/local/lib/python3.9/site-packages/pip/_internal/resolution/resolvelib/candidates.py", line 308, in _prepare_distribution
    return preparer.prepare_linked_requirement(self._ireq, parallel_builds=True)
  File "/usr/local/lib/python3.9/site-packages/pip/_internal/operations/prepare.py", line 491, in prepare_linked_requirement
    return self._prepare_linked_requirement(req, parallel_builds)
  File "/usr/local/lib/python3.9/site-packages/pip/_internal/operations/prepare.py", line 536, in _prepare_linked_requirement
    local_file = unpack_url(
  File "/usr/local/lib/python3.9/site-packages/pip/_internal/operations/prepare.py", line 166, in unpack_url
    file = get_http_url(
  File "/usr/local/lib/python3.9/site-packages/pip/_internal/operations/prepare.py", line 107, in get_http_url
    from_path, content_type = download(link, temp_dir.path)
  File "/usr/local/lib/python3.9/site-packages/pip/_internal/network/download.py", line 147, in __call__
    for chunk in chunks:
  File "/usr/local/lib/python3.9/site-packages/pip/_internal/cli/progress_bars.py", line 53, in _rich_progress_bar
    for chunk in iterable:
  File "/usr/local/lib/python3.9/site-packages/pip/_internal/network/utils.py", line 63, in response_chunks
    for chunk in response.raw.stream(
  File "/usr/local/lib/python3.9/site-packages/pip/_vendor/urllib3/response.py", line 622, in stream
    data = self.read(amt=amt, decode_content=decode_content)
  File "/usr/local/lib/python3.9/site-packages/pip/_vendor/urllib3/response.py", line 587, in read
    raise IncompleteRead(self._fp_bytes_read, self.length_remaining)
  File "/usr/local/lib/python3.9/contextlib.py", line 137, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/usr/local/lib/python3.9/site-packages/pip/_vendor/urllib3/response.py", line 443, in _error_catcher
    raise ReadTimeoutError(self._pool, None, "Read timed out.")
pip._vendor.urllib3.exceptions.ReadTimeoutError: HTTPSConnectionPool(host='files.pythonhosted.org', port=443): Read timed out.

[notice] A new release of pip is available: 23.0.1 -> 25.1.1
[notice] To update, run: pip install --upgrade pip
[0K[31;1mERROR: Job failed: exit code 1
"""
        
        project_path = "E:\\aider-git-repos\\ai-proxy"
        
        print(f"📋 测试参数:")
        print(f"  - 作业ID: {job_info['id']}")
        print(f"  - 作业名称: {job_info['name']}")
        print(f"  - 项目路径: {project_path}")
        print(f"  - 错误类型: 网络超时")
        
        # 测试错误分析
        print("\n🔍 测试错误分析...")
        try:
            error_analysis_result = await global_tool_coordinator.async_coordinator.analyze_job_errors(job_log, job_info)
            print(f"✅ 错误分析: {'成功' if error_analysis_result.success else '失败'}")
            print(f"   消息: {error_analysis_result.message}")
            
            if error_analysis_result.success and error_analysis_result.data:
                all_errors = error_analysis_result.data.get('all_errors', [])
                print(f"   发现错误数: {len(all_errors)}")
                
                # 分析错误类型
                network_errors = []
                config_errors = []
                other_errors = []
                
                for i, error in enumerate(all_errors):
                    error_content = str(error).lower()
                    print(f"   错误{i+1}: {str(error)[:100]}...")
                    
                    if any(keyword in error_content for keyword in ['timeout', 'read timed out', 'network', 'connection']):
                        network_errors.append(error)
                        print(f"     ✅ 正确识别为网络错误")
                    elif any(keyword in error_content for keyword in ['flake8', 'extend-ignore', 'config']):
                        config_errors.append(error)
                        print(f"     ❌ 错误识别为配置错误")
                    else:
                        other_errors.append(error)
                        print(f"     ⚠️ 其他类型错误")
                
                print(f"\n📊 错误分类统计:")
                print(f"  - 网络错误: {len(network_errors)} 个")
                print(f"  - 配置错误: {len(config_errors)} 个")
                print(f"  - 其他错误: {len(other_errors)} 个")
                
                # 验证分析结果
                if len(network_errors) > 0 and len(config_errors) == 0:
                    print("✅ 错误分析正确：正确识别为网络问题")
                    analysis_correct = True
                elif len(config_errors) > 0:
                    print("❌ 错误分析错误：误判为配置问题")
                    analysis_correct = False
                else:
                    print("⚠️ 错误分析不明确：未明确识别错误类型")
                    analysis_correct = False
                    
        except Exception as e:
            print(f"❌ 错误分析失败: {e}")
            print("🔍 详细错误堆栈:")
            traceback.print_exc()
            return False
        
        # 测试修复方案生成
        print("\n🤖 测试修复方案生成...")
        try:
            if error_analysis_result.success and error_analysis_result.data:
                all_errors = error_analysis_result.data.get('all_errors', [])
                
                if all_errors:
                    ai_fix_result = await global_tool_coordinator.async_coordinator._ai_generate_fix_plan(all_errors, project_path)
                    
                    if ai_fix_result.success:
                        print("✅ AI修复方案生成成功")
                        
                        fix_plan = []
                        if ai_fix_result.data and isinstance(ai_fix_result.data, dict):
                            fix_plan = ai_fix_result.data.get('fix_plan', [])
                        
                        if fix_plan:
                            print(f"🤖 AI生成了 {len(fix_plan)} 个修复步骤")
                            
                            # 分析修复方案是否合理
                            network_fixes = []
                            config_fixes = []
                            
                            for i, step in enumerate(fix_plan):
                                step_description = step.get('description', '') if isinstance(step, dict) else str(step)
                                step_command = step.get('command', '') if isinstance(step, dict) else ''
                                
                                print(f"   步骤{i+1}: {step_description}")
                                if step_command:
                                    print(f"     命令: {step_command}")
                                
                                step_content = (step_description + ' ' + step_command).lower()
                                
                                if any(keyword in step_content for keyword in ['retry', 'timeout', 'mirror', 'proxy', 'network']):
                                    network_fixes.append(step)
                                    print(f"     ✅ 正确的网络修复方案")
                                elif any(keyword in step_content for keyword in ['flake8', 'extend-ignore', '.flake8']):
                                    config_fixes.append(step)
                                    print(f"     ❌ 错误的配置修复方案")
                                else:
                                    print(f"     ⚠️ 通用修复方案")
                            
                            print(f"\n📊 修复方案分类:")
                            print(f"  - 网络修复: {len(network_fixes)} 个")
                            print(f"  - 配置修复: {len(config_fixes)} 个")
                            
                            if len(network_fixes) > 0 and len(config_fixes) == 0:
                                print("✅ 修复方案正确：针对网络问题")
                                fix_correct = True
                            elif len(config_fixes) > 0:
                                print("❌ 修复方案错误：针对配置问题")
                                fix_correct = False
                            else:
                                print("⚠️ 修复方案通用：未针对具体问题")
                                fix_correct = False
                        else:
                            print("❌ AI修复方案为空")
                            fix_correct = False
                    else:
                        print("❌ AI修复方案生成失败")
                        fix_correct = False
                else:
                    print("❌ 没有发现错误")
                    fix_correct = False
                    
        except Exception as e:
            print(f"❌ 修复方案生成失败: {e}")
            print("🔍 详细错误堆栈:")
            traceback.print_exc()
            fix_correct = False
        
        print("\n🎯 测试结果总结:")
        print(f"  - 错误分析正确性: {'✅ 正确' if analysis_correct else '❌ 错误'}")
        print(f"  - 修复方案正确性: {'✅ 正确' if fix_correct else '❌ 错误'}")
        
        if analysis_correct and fix_correct:
            print("\n🎉 测试通过！系统能够正确识别和处理网络超时问题")
            return True
        else:
            print("\n❌ 测试失败！系统无法正确处理网络超时问题")
            print("\n💡 问题分析:")
            if not analysis_correct:
                print("  - 错误分析模块需要改进，应该能够识别网络超时错误")
            if not fix_correct:
                print("  - 修复方案生成需要改进，应该针对网络问题而不是配置问题")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print("🔍 详细错误堆栈:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_network_timeout_analysis())
    sys.exit(0 if success else 1)
