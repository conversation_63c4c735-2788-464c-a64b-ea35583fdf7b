#!/usr/bin/env python3
"""
测试轮次编号修复
验证轮次编号和名称的一致性
"""

import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def test_round_numbering_consistency():
    """测试轮次编号一致性修复"""
    print("🔧 测试轮次编号一致性修复")
    print("=" * 50)

    try:
        from bot_agent.utils.conversation_logger import global_conversation_logger
        from bot_agent.utils.conversation_logger import ConversationStatus

        # 创建测试会话
        session_id = global_conversation_logger.start_session(
            task_id="round_consistency_test",
            task_title="轮次编号一致性测试",
            task_type="test",
            project_path="/test/path"
        )

        print(f"✅ 会话ID: {session_id}")

        # 测试场景1：自动分配轮次编号
        print("\n📋 测试场景1：自动分配轮次编号")
        global_conversation_logger.log_round(
            session_id=session_id,
            round_name="初始分析",
            prompt="测试提示1",
            response="测试响应1",
            model_name="test-model",
            duration=0.1
        )
        print("  ✅ 记录轮次1（自动分配）")

        # 测试场景2：手动指定轮次编号（正常情况）
        print("\n📋 测试场景2：手动指定轮次编号（正常情况）")
        global_conversation_logger.log_round(
            session_id=session_id,
            round_number=2,
            round_name="AI生成修复方案",
            prompt="测试提示2",
            response="测试响应2",
            model_name="test-model",
            duration=0.1
        )
        print("  ✅ 记录轮次2（手动指定）")

        # 测试场景3：手动指定轮次编号（跳跃编号）
        print("\n📋 测试场景3：手动指定轮次编号（跳跃编号）")
        global_conversation_logger.log_round(
            session_id=session_id,
            round_number=5,  # 故意跳跃编号
            round_name="跳跃测试",
            prompt="测试提示5",
            response="测试响应5",
            model_name="test-model",
            duration=0.1
        )
        print("  ✅ 记录轮次5（跳跃编号）")

        # 测试场景4：继续自动分配（应该从6开始）
        print("\n📋 测试场景4：继续自动分配（应该从6开始）")
        global_conversation_logger.log_round(
            session_id=session_id,
            round_name="继续自动分配",
            prompt="测试提示6",
            response="测试响应6",
            model_name="test-model",
            duration=0.1
        )
        print("  ✅ 记录轮次6（自动分配）")

        # 测试场景5：round_name已包含轮次编号（但编号不匹配）
        print("\n📋 测试场景5：round_name已包含轮次编号（但编号不匹配）")
        global_conversation_logger.log_round(
            session_id=session_id,
            round_name="第3轮：修复效果验证",  # 故意使用错误的编号
            prompt="测试提示7",
            response="测试响应7",
            model_name="test-model",
            duration=0.1
        )
        print("  ✅ 记录轮次7（包含错误编号，应该被修正）")

        # 测试场景6：round_name包含多重编号前缀
        print("\n📋 测试场景6：round_name包含多重编号前缀")
        global_conversation_logger.log_round(
            session_id=session_id,
            round_name="第1轮：第3轮：修复效果验证",  # 模拟当前的问题
            prompt="测试提示8",
            response="测试响应8",
            model_name="test-model",
            duration=0.1
        )
        print("  ✅ 记录轮次8（多重编号前缀，应该被清理）")

        # 结束会话
        global_conversation_logger.end_session(session_id, "测试完成", ConversationStatus.SUCCESS)

        # 验证结果
        print(f"\n📊 验证结果...")
        session = global_conversation_logger._load_session_from_file(session_id)

        if session and session.rounds:
            print(f"  - 总轮次数: {len(session.rounds)}")

            all_correct = True
            for round_info in session.rounds:
                expected_name_prefix = f"第{round_info.round_number}轮"

                if round_info.round_name.startswith(expected_name_prefix):
                    print(f"  ✅ 轮次{round_info.round_number}: {round_info.round_name}")
                else:
                    print(f"  ❌ 轮次{round_info.round_number}: {round_info.round_name} (不匹配)")
                    all_correct = False

            # 检查轮次编号连续性（允许跳跃）
            round_numbers = [r.round_number for r in session.rounds]
            print(f"  - 轮次编号序列: {round_numbers}")

            # 检查是否有重复编号
            if len(round_numbers) == len(set(round_numbers)):
                print("  ✅ 没有重复的轮次编号")
            else:
                print("  ❌ 存在重复的轮次编号")
                all_correct = False

            if all_correct:
                print(f"\n🎉 轮次编号一致性修复成功！")
                print(f"- 所有轮次的编号和名称都保持一致")
                print(f"- 自动分配和手动指定都工作正常")
                print(f"- 跳跃编号后的自动分配正确")
                return True
            else:
                print(f"\n❌ 仍然存在一致性问题")
                return False
        else:
            print(f"❌ 无法加载测试会话")
            return False

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_webui_display():
    """测试Web UI显示逻辑"""
    print("\n🔧 测试Web UI显示逻辑")
    print("=" * 50)

    # 模拟Web UI的显示逻辑
    test_rounds = [
        {"round_number": 1, "round_name": "第1轮：初始分析"},
        {"round_number": 2, "round_name": "第2轮：AI生成修复方案"},
        {"round_number": 5, "round_name": "第5轮：跳跃测试"},
        {"round_number": 6, "round_name": "第6轮：继续自动分配"},
        {"round_number": 7, "round_name": "第7轮：已包含编号测试"}
    ]

    print("📋 模拟Web UI显示:")

    all_correct = True
    for round_info in test_rounds:
        # 模拟Web UI的显示逻辑
        if round_info["round_name"].startswith(f"第{round_info['round_number']}轮"):
            display_name = round_info["round_name"]
        else:
            display_name = f"第{round_info['round_number']}轮：{round_info['round_name']}"

        print(f"  ✅ {display_name}")

        # 检查是否有重复编号
        duplicate_patterns = [
            f"第{round_info['round_number']}轮: 第{round_info['round_number']}轮",
            f"第{round_info['round_number']}轮：第{round_info['round_number']}轮"
        ]

        for pattern in duplicate_patterns:
            if pattern in display_name:
                print(f"    ❌ 发现重复编号: {display_name}")
                all_correct = False

    if all_correct:
        print("✅ Web UI显示逻辑正常")
        return True
    else:
        print("❌ Web UI显示逻辑有问题")
        return False

def test_real_scenario():
    """测试真实场景中的轮次编号问题"""
    print("\n🔧 测试真实场景中的轮次编号问题")
    print("=" * 50)

    try:
        from bot_agent.utils.conversation_logger import global_conversation_logger
        from bot_agent.utils.conversation_logger import ConversationStatus

        # 创建测试会话
        session_id = global_conversation_logger.start_session(
            task_id="real_scenario_test",
            task_title="真实场景轮次编号测试",
            task_type="job_failure_analysis",
            project_path="/test/path"
        )

        print(f"✅ 会话ID: {session_id}")

        # 模拟真实场景中的轮次记录顺序
        print("\n📋 模拟真实场景轮次记录:")

        # 1. 智能分析过程（通常是第一个被记录的）
        global_conversation_logger.log_round(
            session_id=session_id,
            round_name="智能作业失败分析",
            prompt="智能分析Job失败原因并执行修复",
            response="分析完成，发现flake8配置错误",
            model_name="intelligent-job-analyzer",
            duration=2.5
        )
        print("  ✅ 记录智能分析轮次")

        # 2. 第二轮精准修复（可能有多个）
        for i in range(2):
            global_conversation_logger.log_round(
                session_id=session_id,
                round_name=f"第二轮精准修复{i+1}: flake8配置错误",
                prompt=f"修复flake8配置错误 {i+1}",
                response=f"修复{'成功' if i == 0 else '失败'}",
                model_name="second-round-precise-fixer",
                duration=1.2
            )
            print(f"  ✅ 记录第二轮精准修复{i+1}")

        # 3. 第二轮智能修复
        global_conversation_logger.log_round(
            session_id=session_id,
            round_name="第二轮智能修复",
            prompt="基于第一轮修复结果进行智能修复",
            response="第二轮修复成功",
            model_name="second-round-fixer",
            duration=3.1
        )
        print("  ✅ 记录第二轮智能修复")

        # 4. 修复效果验证
        global_conversation_logger.log_round(
            session_id=session_id,
            round_name="修复效果验证",
            prompt="验证修复效果",
            response="验证成功，所有错误已修复",
            model_name="fix-verifier",
            duration=1.8
        )
        print("  ✅ 记录修复效果验证")

        # 5. AI驱动的多轮修复
        global_conversation_logger.log_round(
            session_id=session_id,
            round_name="AI驱动的多轮修复",
            prompt="AI驱动的多轮修复策略",
            response="多轮修复完成",
            model_name="ai-multi-round-fixer",
            duration=4.2
        )
        print("  ✅ 记录AI驱动的多轮修复")

        # 结束会话
        global_conversation_logger.end_session(session_id, "测试完成", ConversationStatus.SUCCESS)

        # 验证结果
        print(f"\n📊 验证真实场景结果...")
        session = global_conversation_logger._load_session_from_file(session_id)

        if session and session.rounds:
            print(f"  - 总轮次数: {len(session.rounds)}")

            all_correct = True
            for i, round_info in enumerate(session.rounds, 1):
                expected_round_number = i
                actual_round_number = round_info.round_number

                if actual_round_number == expected_round_number:
                    print(f"  ✅ 轮次{actual_round_number}: {round_info.round_name}")
                else:
                    print(f"  ❌ 轮次编号错误: 期望{expected_round_number}, 实际{actual_round_number}: {round_info.round_name}")
                    all_correct = False

                # 检查轮次名称是否包含正确的编号前缀
                if round_info.round_name.startswith(f"第{actual_round_number}轮"):
                    pass  # 正确
                elif not round_info.round_name.startswith("第"):
                    pass  # 没有编号前缀也是正确的
                else:
                    print(f"    ⚠️  轮次名称编号不匹配: {round_info.round_name}")

            if all_correct:
                print(f"\n🎉 真实场景测试通过！")
                print(f"- 轮次编号连续且正确")
                print(f"- 轮次名称格式正确")
                return True
            else:
                print(f"\n❌ 真实场景测试失败")
                return False
        else:
            print(f"❌ 无法加载测试会话")
            return False

    except Exception as e:
        print(f"❌ 真实场景测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始轮次编号修复验证")

    results = []

    # 测试1: 轮次编号一致性
    results.append(test_round_numbering_consistency())

    # 测试2: Web UI显示逻辑
    results.append(test_webui_display())

    # 测试3: 真实场景测试
    results.append(test_real_scenario())

    print(f"\n📊 测试结果汇总:")
    print(f"  - 总测试数: {len(results)}")
    print(f"  - 通过数: {sum(results)}")
    print(f"  - 失败数: {len(results) - sum(results)}")
    print(f"  - 成功率: {sum(results) / len(results) * 100:.1f}%")

    if all(results):
        print(f"\n🎉 所有测试通过！轮次编号问题已修复！")
        print(f"现在轮次编号和名称将保持一致，Web UI显示也不会混乱")
        print(f"\n🔧 修复要点:")
        print(f"1. 移除了所有手动指定的轮次编号")
        print(f"2. 统一使用自动轮次编号分配")
        print(f"3. 修复了轮次名称中的重复编号前缀问题")
        print(f"4. 确保轮次记录的顺序与执行顺序一致")
    else:
        print(f"\n❌ 部分测试失败，需要进一步修复")

if __name__ == "__main__":
    main()
