2025-05-29 20:47:34,415 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 20:47:34,415 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 20:47:34,416 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 20:47:34,417 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-29 20:47:34,417 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-29 20:47:34,419 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 20:47:34,420 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 20:47:35,535 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:89 - _check_connection - Successfully connected to GitLab API: {'version': '17.11.1-ee', 'revision': 'cd4c186e8bc', 'kas': {'enabled': True, 'externalUrl': 'ws://***************/-/kubernetes-agent/', 'externalK8sProxyUrl': 'http://***************/-/kubernetes-agent/k8s-proxy/', 'version': '17.11.1'}, 'enterprise': True}
2025-05-29 20:47:35,536 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-29 20:47:35,536 - bot_agent.handlers.ai_processor - INFO - ai_processor.py:136 - _process_with_aider - 使用AiderBasedTaskExecutor处理任务: 作业失败分析 - lint (Job 944)
2025-05-29 20:47:35,536 - bot_agent.engines.simple_progress_tracker - WARNING - simple_progress_tracker.py:50 - send_progress_update - 缺少项目ID或Issue IID，跳过进度更新
2025-05-29 20:47:35,537 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-29 20:47:35,537 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 20:47:35,537 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 20:47:37,383 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:89 - _check_connection - Successfully connected to GitLab API: {'version': '17.11.1-ee', 'revision': 'cd4c186e8bc', 'kas': {'enabled': True, 'externalUrl': 'ws://***************/-/kubernetes-agent/', 'externalK8sProxyUrl': 'http://***************/-/kubernetes-agent/k8s-proxy/', 'version': '17.11.1'}, 'enterprise': True}
2025-05-29 20:47:37,383 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:99 - __init__ - 使用环境变量中的项目下载目录: E:\aider-git-repos\
2025-05-29 20:47:37,384 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\aider-git-repos\
2025-05-29 20:47:37,384 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-29 20:47:37,385 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:132 - setup_git_config - 使用环境变量中的Git用户名: aider-worker
2025-05-29 20:47:37,385 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:136 - setup_git_config - 使用环境变量中的Git用户邮箱: <EMAIL>
2025-05-29 20:47:37,386 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:143 - setup_git_config - 已设置Git环境变量: GIT_AUTHOR_NAME=aider-worker, GIT_COMMITTER_NAME=aider-worker
2025-05-29 20:47:37,386 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:148 - setup_git_config - 已设置Git环境变量: GIT_AUTHOR_EMAIL=<EMAIL>, GIT_COMMITTER_EMAIL=<EMAIL>
2025-05-29 20:47:37,388 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: information_query
2025-05-29 20:47:37,388 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: TerminalTools
2025-05-29 20:47:37,388 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: TestingTools
2025-05-29 20:47:37,388 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: LogAnalysisTools
2025-05-29 20:47:37,389 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: DatabaseTools
2025-05-29 20:47:37,389 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: DependencyTools
2025-05-29 20:47:37,389 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: DebugTools
2025-05-29 20:47:37,390 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: FrontendDebugTools
2025-05-29 20:47:37,390 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: RefactorTools
2025-05-29 20:47:37,391 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: DocumentationTools
2025-05-29 20:47:37,391 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: SecurityTools
2025-05-29 20:47:37,391 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: CodeGenerationTools
2025-05-29 20:47:37,392 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-29 20:47:37,392 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-29 20:47:37,393 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-29 20:47:37,393 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-29 20:47:37,393 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\aider-git-repos\
2025-05-29 20:47:37,394 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-29 20:47:37,394 - bot_agent.handlers.ai_processor - INFO - ai_processor.py:174 - _process_with_aider - 使用Aider执行引擎处理 TaskType.BUG_FIX 类型任务
2025-05-29 20:47:37,395 - bot_agent.engines.task_executor - INFO - task_executor.py:64 - execute_task - 开始使用Aider执行任务 b4a0a659-a273-4a12-82d2-3126a57375b4
2025-05-29 20:47:37,674 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 📍 注册监控点: prepare_workspace
2025-05-29 20:47:37,674 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目ID: 9
2025-05-29 20:47:37,675 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目信息: 9
2025-05-29 20:47:37,676 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:136 - _make_request - Making GET request to http://***************/api/v4/projects/9
2025-05-29 20:47:39,296 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:155 - _make_request - Response status code: 200
2025-05-29 20:47:39,296 - bot_agent.clients.gitlab_client - DEBUG - gitlab_client.py:160 - _make_request - Response content preview: b'{"id":9,"description":null,"name":"ai-proxy","name_with_namespace":"Longer / ai-proxy","path":"ai-proxy","path_with_namespace":"Longer/ai-proxy","created_at":"2025-05-26T12:38:44.596Z","default_branch":"main","tag_list":[],"topics":[],"ssh_url_to_repo":"git@***************:Longer/ai-proxy.git","http_url_to_repo":"http://***************/Longer/ai-proxy.git","web_url":"http://***************/Longer/ai-proxy","readme_url":"http://***************/Longer/ai-proxy/-/blob/main/README.md","forks_count":'
2025-05-29 20:47:39,297 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:169 - _make_request - Response JSON: {'id': 9, 'description': None, 'name': 'ai-proxy', 'name_with_namespace': 'Longer / ai-proxy', 'path': 'ai-proxy', 'path_with_namespace': 'Longer/ai-proxy', 'created_at': '2025-05-26T12:38:44.596Z', 'default_branch': 'main', 'tag_list': [], 'topics': [], 'ssh_url_to_repo': 'git@***************:Longer/ai-proxy.git', 'http_url_to_repo': 'http://***************/Longer/ai-proxy.git', 'web_url': 'http://***************/Longer/ai-proxy', 'readme_url': 'http://***************/Longer/ai-proxy/-/blob/main/README.md', 'forks_count': 0, 'avatar_url': None, 'star_count': 0, 'last_activity_at': '2025-05-29T11:00:07.268Z', 'namespace': {'id': 3, 'name': 'Longer', 'path': 'Longer', 'kind': 'user', 'full_path': 'Longer', 'parent_id': None, 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'web_url': 'http://***************/Longer'}, 'container_registry_image_prefix': '***************:5050/longer/ai-proxy', '_links': {'self': 'http://***************/api/v4/projects/9', 'issues': 'http://***************/api/v4/projects/9/issues', 'merge_requests': 'http://***************/api/v4/projects/9/merge_requests', 'repo_branches': 'http://***************/api/v4/projects/9/repository/branches', 'labels': 'http://***************/api/v4/projects/9/labels', 'events': 'http://***************/api/v4/projects/9/events', 'members': 'http://***************/api/v4/projects/9/members', 'cluster_agents': 'http://***************/api/v4/projects/9/cluster_agents'}, 'packages_enabled': True, 'empty_repo': False, 'archived': False, 'visibility': 'private', 'owner': {'id': 3, 'username': 'Longer', 'name': 'Longer', 'state': 'active', 'locked': False, 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'web_url': 'http://***************/Longer'}, 'resolve_outdated_diff_discussions': False, 'container_expiration_policy': {'cadence': '1d', 'enabled': False, 'keep_n': 10, 'older_than': '90d', 'name_regex': '.*', 'name_regex_keep': None, 'next_run_at': '2025-05-27T12:38:44.985Z'}, 'repository_object_format': 'sha1', 'issues_enabled': True, 'merge_requests_enabled': True, 'wiki_enabled': True, 'jobs_enabled': True, 'snippets_enabled': True, 'container_registry_enabled': True, 'service_desk_enabled': False, 'service_desk_address': None, 'can_create_merge_request_in': True, 'issues_access_level': 'enabled', 'repository_access_level': 'enabled', 'merge_requests_access_level': 'enabled', 'forking_access_level': 'enabled', 'wiki_access_level': 'enabled', 'builds_access_level': 'enabled', 'snippets_access_level': 'enabled', 'pages_access_level': 'private', 'analytics_access_level': 'enabled', 'container_registry_access_level': 'enabled', 'security_and_compliance_access_level': 'private', 'releases_access_level': 'enabled', 'environments_access_level': 'enabled', 'feature_flags_access_level': 'enabled', 'infrastructure_access_level': 'enabled', 'monitor_access_level': 'enabled', 'model_experiments_access_level': 'enabled', 'model_registry_access_level': 'enabled', 'emails_disabled': False, 'emails_enabled': True, 'shared_runners_enabled': True, 'lfs_enabled': True, 'creator_id': 3, 'import_status': 'none', 'open_issues_count': 1, 'description_html': '', 'updated_at': '2025-05-29T11:45:11.905Z', 'ci_config_path': None, 'public_jobs': True, 'shared_with_groups': [], 'only_allow_merge_if_pipeline_succeeds': False, 'allow_merge_on_skipped_pipeline': None, 'request_access_enabled': True, 'only_allow_merge_if_all_discussions_are_resolved': False, 'remove_source_branch_after_merge': True, 'printing_merge_request_link_enabled': True, 'merge_method': 'merge', 'squash_option': 'default_off', 'enforce_auth_checks_on_uploads': True, 'suggestion_commit_message': None, 'merge_commit_template': None, 'squash_commit_template': None, 'issue_branch_template': None, 'warn_about_potentially_unwanted_characters': True, 'autoclose_referenced_issues': True, 'max_artifacts_size': None, 'requirements_enabled': False, 'requirements_access_level': 'enabled', 'security_and_compliance_enabled': True, 'compliance_frameworks': [], 'permissions': {'project_access': {'access_level': 30, 'notification_level': 3}, 'group_access': None}}
2025-05-29 20:47:39,298 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 项目路径: E:\aider-git-repos\ai-proxy
2025-05-29 20:47:39,299 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 检查本地仓库
2025-05-29 20:47:39,300 - bot_agent.engines.task_executor - INFO - task_executor.py:135 - _prepare_workspace - 使用现有仓库: E:\aider-git-repos\ai-proxy
2025-05-29 20:47:39,301 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 使用现有仓库
2025-05-29 20:47:39,302 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - ✅ 注销监控点: prepare_workspace，执行时间: 1.91s
2025-05-29 20:47:40,767 - bot_agent.memory.project_memory - INFO - project_memory.py:42 - __init__ - ProjectMemoryManager initialized for project: E:\aider-git-repos\ai-proxy
2025-05-29 20:47:40,788 - bot_agent.memory.global_memory - INFO - global_memory.py:60 - load_memory - Global memory loaded
2025-05-29 20:47:40,789 - bot_agent.memory.memory_integration - INFO - memory_integration.py:78 - prepare_context_for_task - Prepared context for task: 925 characters
2025-05-29 20:47:40,789 - bot_agent.engines.tool_integration - INFO - tool_integration.py:49 - enhance_aider_request - 开始增强Aider请求
2025-05-29 20:47:40,789 - bot_agent.tools.tool_router - INFO - tool_router.py:184 - analyze_task - 分析任务: 
任务类型: TaskType.BUG_FIX
任务标题: 作业失败分析 - lint (Job 944)

任务描述:

## 🚨 作业失败自动分析

**项目**: ai-proxy
**作业名称**: lint
**作业ID**: 944
**Pipeline ID**: 254
**阶段**: test
**分支**: aider-plus-dev
**状态**: failed
**失败原因**: script_failure

### 任务要求
1. 分析作业失败的具体原因
2. 收集作业日志和错误信息
3. 提供针对性的修复建议
4. 如果是部署作业，评估回滚需求

### 自动化指令
请立即分析Job 944的失败原因，收集详细日志，并提供修复方案。


# 全局工作记忆
## 相关工作习惯
- task_preference: 成功处理 TaskType.BUG_FIX 类型任务 (频率: 99)
- task_preference: 成功处理 TaskType.PROJECT_ANALYSIS 类型任务 (频率: 4)
- task_preference: 成功处理 OPTIMIZATION 类型任务 (频率: 1)

## 偏好设置
tools: VSCode, PowerShell, GitLab, 2025-05-27T15:23:37.010003, GitLab, aider-plus; programming_languages: Python, JavaScript, Rust, 2025-05-29T19:45:13.131224, go, bug_fix, 1; frameworks: FastAPI, PostgreSQL, Redis, pytest, 2025-05-29T19:05:08.557516, express, 作业失败分析 - lint (Job 932), 1


# 项目记忆
## 项目架构
Overview: AI API代理服务; Patterns: 代理模式, 策略模式, 工厂模式; Technologies: FastAPI, asyncio, httpx

## general 编码规范
- 作业失败分析 - lint (Job 653)
- 作业失败分析 - lint (Job 664)
- 作业失败分析 - lint (Job 677)



# 环境信息
## 环境配置
- last_project_path: E:\aider-git-repos\ai-proxy
- last_task_type: bug_fix
- os: Windows 11
- python_version: 3.9
- conda_env: aider-plus
- git_user: aider-worker
- workspace: E:\Projects\aider-plus
- last_used_tool: aider


## 🚨 严格执行指令 - 禁止偏离

### ⚠️ 绝对禁止的行为：
1. **禁止创建任何新文件** - 不允许创建.py、.js、.md等任何新文件
2. **禁止编写代码** - 不允许编写类、函数、测试代码
3. **禁止基于假设分析** - 必须基于真实数据和日志
4. **禁止提供通用建议** - 必须针对具体问题提供具体解决方案
5. **禁止代码审查** - 当前任务不是代码审查，是问题分析

### ✅ 必须执行的步骤（严格按顺序）：

#### 第1步：获取真实数据（必须完成）
- 使用GitLabClient获取Job的实际日志内容
- 如果无法获取，明确说明原因并停止
- 不允许基于"没有日志"进行假设性分析

#### 第2步：分析具体问题（基于真实数据）
- 使用LogAnalysisTools分析实际的错误日志
- 识别具体的错误类型、文件、行号
- 确定失败的根本原因

#### 第3步：执行具体修复（针对性解决）
- 使用TerminalTools执行针对性的修复命令
- 修复具体识别出的问题
- 不执行通用的格式化命令

#### 第4步：验证修复效果
- 使用TestingTools验证修复是否成功
- 确认问题已解决

### 🎯 当前任务要求：
如果这是作业失败分析任务，你必须：
1. 获取指定Job ID的实际失败日志
2. 分析日志中的具体错误信息
3. 提供针对这些具体错误的修复方案
4. 验证修复效果

### 🚫 严格禁止：
- 说"Since we don't have the actual log files"
- 提供black、flake8等通用命令
- 创建LintAnalyzer等新类
- 进行代码审查
- 创建测试文件

现在开始执行，严格遵循上述要求：

2025-05-29 20:47:40,792 - bot_agent.tools.tool_router - DEBUG - tool_router.py:252 - _calculate_confidence - 工具匹配分析 - 关键词匹配: 4, 模式匹配: 0, 最终置信度: 0.54
2025-05-29 20:47:40,793 - bot_agent.tools.tool_router - DEBUG - tool_router.py:252 - _calculate_confidence - 工具匹配分析 - 关键词匹配: 3, 模式匹配: 1, 最终置信度: 0.5199999999999999
2025-05-29 20:47:40,793 - bot_agent.tools.tool_router - DEBUG - tool_router.py:252 - _calculate_confidence - 工具匹配分析 - 关键词匹配: 5, 模式匹配: 1, 最终置信度: 0.6299999999999999
2025-05-29 20:47:40,794 - bot_agent.tools.tool_router - DEBUG - tool_router.py:252 - _calculate_confidence - 工具匹配分析 - 关键词匹配: 5, 模式匹配: 1, 最终置信度: 0.8099999999999999
2025-05-29 20:47:40,795 - bot_agent.tools.tool_router - DEBUG - tool_router.py:252 - _calculate_confidence - 工具匹配分析 - 关键词匹配: 2, 模式匹配: 0, 最终置信度: 0.24
2025-05-29 20:47:40,795 - bot_agent.tools.tool_router - DEBUG - tool_router.py:252 - _calculate_confidence - 工具匹配分析 - 关键词匹配: 1, 模式匹配: 0, 最终置信度: 0.105
2025-05-29 20:47:40,795 - bot_agent.tools.tool_router - INFO - tool_router.py:217 - analyze_task - 生成了 6 个工具建议
2025-05-29 20:47:40,796 - bot_agent.engines.tool_integration - INFO - tool_integration.py:67 - enhance_aider_request - 请求增强完成，添加了 6 个工具建议
2025-05-29 20:47:40,796 - bot_agent.config.model_config - DEBUG - model_config.py:56 - get_code_generation_model - 获取代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-29 20:47:40,797 - bot_agent.engines.task_executor - INFO - task_executor.py:252 - _execute_with_aider - 使用代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-29 20:47:51,652 - bot_agent.engines.task_executor - INFO - task_executor.py:404 - _smart_file_discovery - 🔍 开始智能文件发现...
2025-05-29 20:47:51,787 - bot_agent.engines.task_executor - INFO - task_executor.py:425 - _smart_file_discovery - 智能发现了 29 个相关文件: ['tests\\test_job_failure_analysis.py', 'requirements.txt', 'tests\\test_provider_security.py', 'tests\\test_job_analysis_unit.py', 'tests\\test_proxy_service_integration.py', 'tests\\test_health_check.py', 'api_proxy\\config.py', 'tests\\test_job_failure_analysis_unit.py', 'tests\\test_lint_analysis_error.py', 'tests\\test_provider_initialization.py', 'setup.py', 'api_proxy\\__init__.py', 'api_proxy\\providers\\__init__.py', 'tests\\test_job_analysis_boundary.py', 'tests\\test_lint_analysis_integration.py', 'tests\\test_lint_analysis_unit.py', 'tests\\test_job_analysis_integration.py', 'tests\\test_job_analysis_error.py', 'tests\\test_sensitive_data.py', 'tests\\test_lint_analysis_boundary.py', 'api_proxy\\models.py', 'tests\\test_provider_boundary.py', 'tests\\test_proxy_service_boundary.py', 'tests\\test_job_failure_analysis_error.py', 'tests\\test_job_failure_analysis_boundary.py', 'tests\\test_proxy_service_error.py', 'tests\\test_job_failure_analysis_integration.py', 'tests\\test_proxy_service_unit.py', 'tests\\test_job_lint_analysis.py']
2025-05-29 20:47:51,792 - bot_agent.engines.task_executor - INFO - task_executor.py:280 - _execute_with_aider - 🔍 启用Aider操作监控系统
2025-05-29 20:47:53,629 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:49 - log_operation - [AIDER_MONITOR] ✅ coder_created: 创建监控的Coder
2025-05-29 20:47:53,630 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:53 - log_operation - [AIDER_MONITOR]   kwargs: {'main_model': Model(name='openrouter/deepseek/deepseek-chat-v3-0324:free', edit_format='diff', weak_model_name='openrouter/deepseek/deepseek-chat-v3-0324:free', use_repo_map=True, send_undo_reply=False, lazy=False, overeager=False, reminder='user', examples_as_sys_msg=True, extra_params=None, cache_control=False, caches_by_default=True, use_system_prompt=True, use_temperature=0.3, streaming=True, editor_model_name='openrouter/deepseek/deepseek-r1:free', editor_edit_format='editor-diff', reasoning_tag=None, remove_reasoning=None, system_prompt_prefix=None, accepts_settings=[]), 'io': <bot_agent.aider_extensions.aider_monitor.MonitoredIO object at 0x000001DB67E2D8E0>, 'repo': <aider.repo.GitRepo object at 0x000001DB6250FF80>, 'fnames': ['tests\\test_job_failure_analysis.py', 'requirements.txt', 'tests\\test_provider_security.py', 'tests\\test_job_analysis_unit.py', 'tests\\test_proxy_service_integration.py', 'tests\\test_health_check.py', 'api_proxy\\config.py', 'tests\\test_job_failure_analysis_unit.py', 'tests\\test_lint_analysis_error.py', 'tests\\test_provider_initialization.py', 'setup.py', 'api_proxy\\__init__.py', 'api_proxy\\providers\\__init__.py', 'tests\\test_job_analysis_boundary.py', 'tests\\test_lint_analysis_integration.py', 'tests\\test_lint_analysis_unit.py', 'tests\\test_job_analysis_integration.py', 'tests\\test_job_analysis_error.py', 'tests\\test_sensitive_data.py', 'tests\\test_lint_analysis_boundary.py', 'api_proxy\\models.py', 'tests\\test_provider_boundary.py', 'tests\\test_proxy_service_boundary.py', 'tests\\test_job_failure_analysis_error.py', 'tests\\test_job_failure_analysis_boundary.py', 'tests\\test_proxy_service_error.py', 'tests\\test_job_failure_analysis_integration.py', 'tests\\test_proxy_service_unit.py', 'tests\\test_job_lint_analysis.py'], 'use_git': False, 'auto_commits': False, 'dirty_commits': False, 'auto_lint': False, 'auto_test': False, 'stream': False, 'verbose': False, 'chat_language': 'Chinese', 'suggest_shell_commands': False, 'auto_accept_architect': True, 'map_tokens': 0, 'cache_prompts': False, 'num_cache_warming_pings': 0}
2025-05-29 20:47:53,632 - bot_agent.engines.task_executor - INFO - task_executor.py:302 - _execute_with_aider - 使用优化的Aider执行任务: 作业失败分析 - lint (Job 944)
2025-05-29 20:47:53,634 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 📍 注册监控点: prepare_workspace
2025-05-29 20:47:53,635 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目ID: 9
2025-05-29 20:47:53,636 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目信息: 9
2025-05-29 20:47:53,636 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:136 - _make_request - Making GET request to http://***************/api/v4/projects/9
2025-05-29 20:47:59,317 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:155 - _make_request - Response status code: 200
2025-05-29 20:47:59,317 - bot_agent.clients.gitlab_client - DEBUG - gitlab_client.py:160 - _make_request - Response content preview: b'{"id":9,"description":null,"name":"ai-proxy","name_with_namespace":"Longer / ai-proxy","path":"ai-proxy","path_with_namespace":"Longer/ai-proxy","created_at":"2025-05-26T12:38:44.596Z","default_branch":"main","tag_list":[],"topics":[],"ssh_url_to_repo":"git@***************:Longer/ai-proxy.git","http_url_to_repo":"http://***************/Longer/ai-proxy.git","web_url":"http://***************/Longer/ai-proxy","readme_url":"http://***************/Longer/ai-proxy/-/blob/main/README.md","forks_count":'
2025-05-29 20:47:59,318 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:169 - _make_request - Response JSON: {'id': 9, 'description': None, 'name': 'ai-proxy', 'name_with_namespace': 'Longer / ai-proxy', 'path': 'ai-proxy', 'path_with_namespace': 'Longer/ai-proxy', 'created_at': '2025-05-26T12:38:44.596Z', 'default_branch': 'main', 'tag_list': [], 'topics': [], 'ssh_url_to_repo': 'git@***************:Longer/ai-proxy.git', 'http_url_to_repo': 'http://***************/Longer/ai-proxy.git', 'web_url': 'http://***************/Longer/ai-proxy', 'readme_url': 'http://***************/Longer/ai-proxy/-/blob/main/README.md', 'forks_count': 0, 'avatar_url': None, 'star_count': 0, 'last_activity_at': '2025-05-29T11:00:07.268Z', 'namespace': {'id': 3, 'name': 'Longer', 'path': 'Longer', 'kind': 'user', 'full_path': 'Longer', 'parent_id': None, 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'web_url': 'http://***************/Longer'}, 'container_registry_image_prefix': '***************:5050/longer/ai-proxy', '_links': {'self': 'http://***************/api/v4/projects/9', 'issues': 'http://***************/api/v4/projects/9/issues', 'merge_requests': 'http://***************/api/v4/projects/9/merge_requests', 'repo_branches': 'http://***************/api/v4/projects/9/repository/branches', 'labels': 'http://***************/api/v4/projects/9/labels', 'events': 'http://***************/api/v4/projects/9/events', 'members': 'http://***************/api/v4/projects/9/members', 'cluster_agents': 'http://***************/api/v4/projects/9/cluster_agents'}, 'packages_enabled': True, 'empty_repo': False, 'archived': False, 'visibility': 'private', 'owner': {'id': 3, 'username': 'Longer', 'name': 'Longer', 'state': 'active', 'locked': False, 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'web_url': 'http://***************/Longer'}, 'resolve_outdated_diff_discussions': False, 'container_expiration_policy': {'cadence': '1d', 'enabled': False, 'keep_n': 10, 'older_than': '90d', 'name_regex': '.*', 'name_regex_keep': None, 'next_run_at': '2025-05-27T12:38:44.985Z'}, 'repository_object_format': 'sha1', 'issues_enabled': True, 'merge_requests_enabled': True, 'wiki_enabled': True, 'jobs_enabled': True, 'snippets_enabled': True, 'container_registry_enabled': True, 'service_desk_enabled': False, 'service_desk_address': None, 'can_create_merge_request_in': True, 'issues_access_level': 'enabled', 'repository_access_level': 'enabled', 'merge_requests_access_level': 'enabled', 'forking_access_level': 'enabled', 'wiki_access_level': 'enabled', 'builds_access_level': 'enabled', 'snippets_access_level': 'enabled', 'pages_access_level': 'private', 'analytics_access_level': 'enabled', 'container_registry_access_level': 'enabled', 'security_and_compliance_access_level': 'private', 'releases_access_level': 'enabled', 'environments_access_level': 'enabled', 'feature_flags_access_level': 'enabled', 'infrastructure_access_level': 'enabled', 'monitor_access_level': 'enabled', 'model_experiments_access_level': 'enabled', 'model_registry_access_level': 'enabled', 'emails_disabled': False, 'emails_enabled': True, 'shared_runners_enabled': True, 'lfs_enabled': True, 'creator_id': 3, 'import_status': 'none', 'open_issues_count': 1, 'description_html': '', 'updated_at': '2025-05-29T11:45:11.905Z', 'ci_config_path': None, 'public_jobs': True, 'shared_with_groups': [], 'only_allow_merge_if_pipeline_succeeds': False, 'allow_merge_on_skipped_pipeline': None, 'request_access_enabled': True, 'only_allow_merge_if_all_discussions_are_resolved': False, 'remove_source_branch_after_merge': True, 'printing_merge_request_link_enabled': True, 'merge_method': 'merge', 'squash_option': 'default_off', 'enforce_auth_checks_on_uploads': True, 'suggestion_commit_message': None, 'merge_commit_template': None, 'squash_commit_template': None, 'issue_branch_template': None, 'warn_about_potentially_unwanted_characters': True, 'autoclose_referenced_issues': True, 'max_artifacts_size': None, 'requirements_enabled': False, 'requirements_access_level': 'enabled', 'security_and_compliance_enabled': True, 'compliance_frameworks': [], 'permissions': {'project_access': {'access_level': 30, 'notification_level': 3}, 'group_access': None}}
2025-05-29 20:47:59,320 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 项目路径: E:\aider-git-repos\ai-proxy
2025-05-29 20:47:59,320 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 检查本地仓库
2025-05-29 20:47:59,320 - bot_agent.engines.task_executor - INFO - task_executor.py:135 - _prepare_workspace - 使用现有仓库: E:\aider-git-repos\ai-proxy
2025-05-29 20:47:59,321 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 使用现有仓库
2025-05-29 20:47:59,321 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - ✅ 注销监控点: prepare_workspace，执行时间: 5.69s
2025-05-29 20:47:59,321 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:132 - start_session - Started conversation session: task_1748522879_1748522879
2025-05-29 20:47:59,322 - bot_agent.engines.task_executor - INFO - task_executor.py:773 - _intelligent_task_execution - 🤖 开始智能任务执行...
2025-05-29 20:47:59,322 - bot_agent.engines.task_executor - INFO - task_executor.py:777 - _intelligent_task_execution - 🔍 检测到作业失败分析任务，使用专门的分析器
2025-05-29 20:47:59,324 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 📍 注册监控点: job_failure_analysis
2025-05-29 20:47:59,325 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 开始作业失败分析
2025-05-29 20:47:59,325 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 提取Job ID
2025-05-29 20:47:59,325 - bot_agent.engines.task_executor - INFO - task_executor.py:1063 - _handle_job_failure_analysis - 提取到Job ID: 944
2025-05-29 20:47:59,326 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 成功提取Job ID: 944
2025-05-29 20:47:59,326 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 获取项目ID
2025-05-29 20:47:59,327 - bot_agent.engines.task_executor - INFO - task_executor.py:1071 - _handle_job_failure_analysis - 从任务metadata中获取到项目ID: 9
2025-05-29 20:47:59,327 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 获取到项目ID: 9
2025-05-29 20:47:59,327 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 准备工作空间
2025-05-29 20:47:59,328 - bot_agent.engines.task_executor - INFO - task_executor.py:1086 - _handle_job_failure_analysis - 作业失败分析使用项目路径: E:\aider-git-repos\ai-proxy
2025-05-29 20:47:59,328 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 项目路径: E:\aider-git-repos\ai-proxy
2025-05-29 20:47:59,329 - bot_agent.engines.task_executor - INFO - task_executor.py:1092 - _handle_job_failure_analysis - 已更新会话 task_1748522879_1748522879 的项目路径为: E:\aider-git-repos\ai-proxy
2025-05-29 20:47:59,329 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 会话路径已更新
2025-05-29 20:47:59,329 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 开始智能分析
2025-05-29 20:47:59,330 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 预执行数据收集
2025-05-29 20:47:59,333 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 20:47:59,334 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 20:47:59,777 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:89 - _check_connection - Successfully connected to GitLab API: {'version': '17.11.1-ee', 'revision': 'cd4c186e8bc', 'kas': {'enabled': True, 'externalUrl': 'ws://***************/-/kubernetes-agent/', 'externalK8sProxyUrl': 'http://***************/-/kubernetes-agent/k8s-proxy/', 'version': '17.11.1'}, 'enterprise': True}
2025-05-29 20:47:59,778 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 20:47:59,879 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:70 - get_job_info_and_log - 🔍 获取Job 944的信息和日志...
2025-05-29 20:47:59,879 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:437 - get_job - Getting job 944 in project 9
2025-05-29 20:47:59,880 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:136 - _make_request - Making GET request to http://***************/api/v4/projects/9/jobs/944
2025-05-29 20:48:02,478 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:155 - _make_request - Response status code: 200
2025-05-29 20:48:02,478 - bot_agent.clients.gitlab_client - DEBUG - gitlab_client.py:160 - _make_request - Response content preview: b'{"id":944,"status":"failed","stage":"test","name":"lint","ref":"aider-plus-dev","tag":false,"coverage":null,"allow_failure":false,"created_at":"2025-05-29T12:45:19.161Z","started_at":"2025-05-29T12:45:23.876Z","finished_at":"2025-05-29T12:47:06.637Z","erased_at":null,"duration":102.760996,"queued_duration":3.050154,"user":{"id":3,"username":"Longer","name":"Longer","state":"active","locked":false,"avatar_url":"https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249'
2025-05-29 20:48:02,479 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:169 - _make_request - Response JSON: {'id': 944, 'status': 'failed', 'stage': 'test', 'name': 'lint', 'ref': 'aider-plus-dev', 'tag': False, 'coverage': None, 'allow_failure': False, 'created_at': '2025-05-29T12:45:19.161Z', 'started_at': '2025-05-29T12:45:23.876Z', 'finished_at': '2025-05-29T12:47:06.637Z', 'erased_at': None, 'duration': 102.760996, 'queued_duration': 3.050154, 'user': {'id': 3, 'username': 'Longer', 'name': 'Longer', 'state': 'active', 'locked': False, 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'web_url': 'http://***************/Longer', 'created_at': '2025-04-27T02:44:28.626Z', 'bio': '', 'location': '', 'public_email': None, 'skype': '', 'linkedin': '', 'twitter': '', 'discord': '', 'website_url': '', 'organization': '', 'job_title': '', 'pronouns': None, 'bot': False, 'work_information': None, 'followers': 0, 'following': 0, 'local_time': None}, 'commit': {'id': '3c0e39a02aa5e03f59c9d410316f14eb6a426cd2', 'short_id': '3c0e39a0', 'created_at': '2025-05-29T19:45:07.000+08:00', 'parent_ids': ['76fb7b736e451d9774b53c29f4463c967390d258'], 'title': 'AI自动修改: 作业失败分析 - lint (Job 938)', 'message': 'AI自动修改: 作业失败分析 - lint (Job 938)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'authored_date': '2025-05-29T19:45:07.000+08:00', 'committer_name': 'Longer (aider)', 'committer_email': '<EMAIL>', 'committed_date': '2025-05-29T19:45:07.000+08:00', 'trailers': {}, 'extended_trailers': {}, 'web_url': 'http://***************/Longer/ai-proxy/-/commit/3c0e39a02aa5e03f59c9d410316f14eb6a426cd2'}, 'pipeline': {'id': 254, 'iid': 76, 'project_id': 9, 'sha': '3c0e39a02aa5e03f59c9d410316f14eb6a426cd2', 'ref': 'aider-plus-dev', 'status': 'failed', 'source': 'push', 'created_at': '2025-05-29T11:45:12.654Z', 'updated_at': '2025-05-29T12:47:10.813Z', 'web_url': 'http://***************/Longer/ai-proxy/-/pipelines/254'}, 'failure_reason': 'script_failure', 'web_url': 'http://***************/Longer/ai-proxy/-/jobs/944', 'project': {'ci_job_token_scope_enabled': False}, 'artifacts': [], 'runner': {'id': 1, 'description': 'docker-runner-137', 'ip_address': None, 'active': True, 'paused': False, 'is_shared': True, 'runner_type': 'instance_type', 'name': None, 'online': True, 'status': 'online'}, 'runner_manager': {'id': 2, 'system_id': 'r_zhwE8WyyfNZL', 'version': '17.11.0', 'revision': '0f67ff19', 'platform': 'linux', 'architecture': 'amd64', 'created_at': '2025-04-27T09:00:17.697Z', 'contacted_at': '2025-05-29T12:47:07.649Z', 'ip_address': '***************', 'status': 'online'}, 'artifacts_expire_at': None, 'archived': False, 'tag_list': []}
2025-05-29 20:48:02,480 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:444 - get_job - 成功获取作业信息: Job 944 - lint (failed)
2025-05-29 20:48:02,480 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:466 - get_job_log - Getting log for job 944 in project 9
2025-05-29 20:48:02,873 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:484 - get_job_log - 成功获取作业日志: Job 944, 长度: 9907 字符
2025-05-29 20:48:02,873 - bot_agent.clients.gitlab_client - DEBUG - gitlab_client.py:487 - get_job_log - 日志前5行: ['\x1b[0KRunning with gitlab-runner 17.11.0 (0f67ff19)\x1b[0;m', '\x1b[0K  on docker-runner-137 dDoxCnU6M, system ID: r_zhwE8WyyfNZL\x1b[0;m', 'section_start:1748522725:prepare_executor\r\x1b[0K\x1b[0K\x1b[36;1mPreparing the "docker" executor\x1b[0;m\x1b[0;m', '\x1b[0KUsing Docker executor with image python:3.9-slim ...\x1b[0;m', '\x1b[0KUsing locally found image version due to "if-not-present" pull policy\x1b[0;m']
2025-05-29 20:48:02,874 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:126 - analyze_job_errors - 🔍 开始智能错误分析...
2025-05-29 20:48:02,874 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:535 - analyze_job_log - 🔍 开始分析作业日志...
2025-05-29 20:48:02,876 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:262 - analyze_logs - 开始分析日志文件: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp4i5ycg5t.log']
2025-05-29 20:48:02,897 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:550 - analyze_job_log - ✅ 作业日志分析完成
2025-05-29 20:48:02,899 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 调用AI进行智能分析
2025-05-29 20:48:02,899 - bot_agent.engines.task_executor - INFO - task_executor.py:1198 - _handle_job_failure_analysis - 🤖 开始调用真实AI进行作业失败分析...
2025-05-29 20:48:02,923 - bot_agent.utils.aider_error_handler - INFO - aider_error_handler.py:31 - __init__ - AiderErrorHandler initialized for E:\aider-git-repos\ai-proxy
2025-05-29 20:48:02,924 - bot_agent.handlers.aider_integration - INFO - aider_integration.py:43 - _check_aider_availability - Aider 模块导入成功
2025-05-29 20:48:02,924 - bot_agent.engines.task_executor - INFO - task_executor.py:1205 - _handle_job_failure_analysis - ✅ Aider可用，使用真实AI模型进行分析
2025-05-29 20:48:02,931 - bot_agent.handlers.aider_integration - ERROR - aider_integration.py:421 - _run_aider_request - 运行 Aider AI 处理请求时出错: Coder.__init__() got an unexpected keyword argument 'browser'
Traceback (most recent call last):
  File "E:\Projects\aider-plus\bot_agent\handlers\aider_integration.py", line 360, in _run_aider_request
    raise coder_error[0]
  File "E:\Projects\aider-plus\bot_agent\handlers\aider_integration.py", line 351, in thread_target
    coder_result[0] = create_coder_in_thread()
                      ^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\bot_agent\handlers\aider_integration.py", line 332, in create_coder_in_thread
    return self.coder_class.create(
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\aider\coders\base_coder.py", line 181, in create
    res = coder(main_model, io, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: Coder.__init__() got an unexpected keyword argument 'browser'
2025-05-29 20:48:02,945 - bot_agent.config.model_config - DEBUG - model_config.py:44 - get_analysis_model - 获取推理分析模型: openrouter/deepseek/deepseek-r1:free
2025-05-29 20:48:02,945 - bot_agent.config.model_config - DEBUG - model_config.py:84 - convert_aider_to_openrouter_format - 转换模型名称: openrouter/deepseek/deepseek-r1:free -> deepseek/deepseek-r1:free
2025-05-29 20:48:02,946 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - AI分析完成，使用模型: deepseek/deepseek-r1:free
2025-05-29 20:48:02,948 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:241 - log_round - Logged round 1 for session task_1748522879_1748522879: 第1轮：智能作业失败分析
2025-05-29 20:48:02,949 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 启动多轮交互智能修复
2025-05-29 20:48:02,949 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 执行多轮交互修复
2025-05-29 20:48:02,950 - bot_agent.engines.task_executor - INFO - task_executor.py:1635 - _execute_multi_round_intelligent_fix_with_logging - 🚀 启动多轮交互智能修复系统...
2025-05-29 20:48:02,950 - bot_agent.engines.task_executor - INFO - task_executor.py:1648 - _execute_multi_round_intelligent_fix_with_logging - 🔍 发现 6 个错误，开始智能修复...
2025-05-29 20:48:02,950 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:519 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 20:48:02,953 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:524 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 20:48:02,953 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\aider-git-repos\ai-proxy
2025-05-29 20:48:08,776 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 20:48:08,777 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 20:48:10,910 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 20:48:10,911 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 20:48:10,911 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 20:48:10,913 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:241 - log_round - Logged round 2 for session task_1748522879_1748522879: 第2轮：AI重试1: provider_error
2025-05-29 20:48:10,914 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 1): cannot access free variable 'ai_prompt' where it is not associated with a value in enclosing scope
2025-05-29 20:48:10,914 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 1.2 秒后重试...
2025-05-29 20:48:10,916 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-29 20:48:10,916 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-29 20:48:10,916 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-29 20:48:10,917 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '60319d94-4746-409e-a456-1238953e20ae', 'x-gitlab-event': 'Pipeline Hook', 'x-gitlab-webhook-uuid': 'c4aa55b3-4864-411e-9013-9e6252faea73', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'f20dc46b-f68d-4e12-8a76-27e111dc5046', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '3466'}
2025-05-29 20:48:10,917 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-29 20:48:10,918 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-29 20:48:10,918 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-29 20:48:10,918 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-29 20:48:10,919 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '60319d94-4746-409e-a456-1238953e20ae', 'x-gitlab-event': 'Pipeline Hook', 'x-gitlab-webhook-uuid': 'c4aa55b3-4864-411e-9013-9e6252faea73', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'f20dc46b-f68d-4e12-8a76-27e111dc5046', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '3466'}
2025-05-29 20:48:10,919 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'pipeline', 'object_attributes': {'id': 254, 'iid': 76, 'name': None, 'ref': 'aider-plus-dev', 'tag': False, 'sha': '3c0e39a02aa5e03f59c9d410316f14eb6a426cd2', 'before_sha': '76fb7b736e451d9774b53c29f4463c967390d258', 'source': 'push', 'status': 'failed', 'detailed_status': 'failed', 'stages': ['test', 'build'], 'created_at': '2025-05-29 11:45:12 UTC', 'finished_at': '2025-05-29 12:47:10 UTC', 'duration': 449, 'queued_duration': 10, 'variables': [], 'url': 'http://***************/Longer/ai-proxy/-/pipelines/254'}, 'merge_request': None, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'commit': {'id': '3c0e39a02aa5e03f59c9d410316f14eb6a426cd2', 'message': 'AI自动修改: 作业失败分析 - lint (Job 938)\n', 'title': 'AI自动修改: 作业失败分析 - lint (Job 938)', 'timestamp': '2025-05-29T19:45:07+08:00', 'url': 'http://***************/Longer/ai-proxy/-/commit/3c0e39a02aa5e03f59c9d410316f14eb6a426cd2', 'author': {'name': 'Longer (aider)', 'email': '<EMAIL>'}}, 'builds': [{'id': 940, 'stage': 'test', 'name': 'test', 'status': 'failed', 'created_at': '2025-05-29 11:45:12 UTC', 'started_at': '2025-05-29 11:45:20 UTC', 'finished_at': '2025-05-29 11:51:07 UTC', 'duration': 347.016582, 'queued_duration': 3.024727, 'failure_reason': 'script_failure', 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': {'id': 1, 'description': 'docker-runner-137', 'runner_type': 'instance_type', 'active': True, 'is_shared': True, 'tags': ['Dev-137']}, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}, {'id': 944, 'stage': 'test', 'name': 'lint', 'status': 'failed', 'created_at': '2025-05-29 12:45:19 UTC', 'started_at': '2025-05-29 12:45:23 UTC', 'finished_at': '2025-05-29 12:47:06 UTC', 'duration': 102.760996, 'queued_duration': 3.050154, 'failure_reason': 'script_failure', 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': {'id': 1, 'description': 'docker-runner-137', 'runner_type': 'instance_type', 'active': True, 'is_shared': True, 'tags': ['Dev-137']}, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}, {'id': 942, 'stage': 'build', 'name': 'build', 'status': 'skipped', 'created_at': '2025-05-29 11:45:12 UTC', 'started_at': None, 'finished_at': None, 'duration': None, 'queued_duration': None, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': None, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}]}
2025-05-29 20:48:10,922 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Pipeline Hook
2025-05-29 20:48:10,922 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Pipeline Hook, object_kind: pipeline, action: unknown
2025-05-29 20:48:10,923 - bot_agent.webhook.gitlab - INFO - gitlab.py:950 - handle_pipeline_event - Processing Pipeline event: Pipeline 254 for aider-plus-dev is failed (Project: ai-proxy, User: Longer)
2025-05-29 20:48:10,923 - bot_agent.webhook.gitlab - INFO - gitlab.py:1082 - handle_pipeline_event - Pipeline 254 status failed recorded (no AI monitoring needed)
2025-05-29 20:48:10,923 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Pipeline 254 status failed recorded'}
2025-05-29 20:48:12,086 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:241 - log_round - Logged round 3 for session task_1748522879_1748522879: 第3轮：AI重试2: provider_error
2025-05-29 20:48:12,087 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 2): cannot access free variable 'ai_prompt' where it is not associated with a value in enclosing scope
2025-05-29 20:48:12,087 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 2.5 秒后重试...
2025-05-29 20:48:14,634 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:241 - log_round - Logged round 4 for session task_1748522879_1748522879: 第4轮：AI重试3: provider_error
2025-05-29 20:48:14,634 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 3): cannot access free variable 'ai_prompt' where it is not associated with a value in enclosing scope
2025-05-29 20:48:14,634 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 4.5 秒后重试...
2025-05-29 20:48:19,172 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:160 - execute_with_retry - 达到最大重试次数，使用降级策略
2025-05-29 20:48:19,173 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:743 - _ai_generate_fix_plan - AI响应JSON解析失败: Expecting value: line 2 column 1 (char 1)
2025-05-29 20:48:19,173 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:750 - _ai_generate_fix_plan - 文本解析也失败，使用智能fallback策略
2025-05-29 20:48:19,174 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:831 - _create_intelligent_fallback_plan_async - 🧠 创建智能fallback修复计划...
2025-05-29 20:48:19,174 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:835 - _create_intelligent_fallback_plan_async - 🔍 原始日志长度: 0
2025-05-29 20:48:19,175 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:837 - _create_intelligent_fallback_plan_async - 🎯 识别的错误模式: {'network_timeout': [{'type': 'network_timeout_error', 'severity': 'medium', 'category': 'network', 'line_number': 73, 'file_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp4i5ycg5t.log', 'content': 'socket.timeout: The read operation timed out', 'solutions': ['检查网络连接', '使用国内镜像源', '增加超时时间', '重试下载操作'], 'timestamp': None}, {'type': 'network_timeout_error', 'severity': 'medium', 'category': 'network', 'line_number': 135, 'file_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp4i5ycg5t.log', 'content': 'raise ReadTimeoutError(self._pool, None, "Read timed out.")', 'solutions': ['检查网络连接', '使用国内镜像源', '增加超时时间', '重试下载操作'], 'timestamp': None}, {'type': 'network_timeout_error', 'severity': 'medium', 'category': 'network', 'line_number': 136, 'file_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp4i5ycg5t.log', 'content': "pip._vendor.urllib3.exceptions.ReadTimeoutError: HTTPSConnectionPool(host='files.pythonhosted.org', port=443): Read timed out.", 'solutions': ['检查网络连接', '使用国内镜像源', '增加超时时间', '重试下载操作'], 'timestamp': None}]}
2025-05-29 20:48:19,177 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:241 - log_round - Logged round 5 for session task_1748522879_1748522879: 第5轮：AI生成修复方案
2025-05-29 20:48:19,177 - bot_agent.engines.task_executor - INFO - task_executor.py:1711 - _execute_multi_round_intelligent_fix_with_logging - 🤖 AI生成了 2 个修复步骤
2025-05-29 20:48:19,178 - bot_agent.engines.task_executor - INFO - task_executor.py:1724 - _execute_multi_round_intelligent_fix_with_logging - 🔧 执行修复步骤 1/2: 使用国内镜像源重试pip安装
2025-05-29 20:48:19,178 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1336 - _execute_ai_fix_step - 🔧 执行AI修复步骤: 使用国内镜像源重试pip安装
2025-05-29 20:48:19,179 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1337 - _execute_ai_fix_step - 📝 执行命令: pip install --timeout 60 --retries 3 -i https://pypi.tuna.tsinghua.edu.cn/simple/ black flake8
2025-05-29 20:48:19,179 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1368 - _execute_command_with_retry - 🔄 第 1 次尝试执行: pip install --timeout 60 --retries 3 -i https://pypi.tuna.tsinghua.edu.cn/simple/ black flake8
2025-05-29 20:48:24,387 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "pip install --timeout 60 --retries 3 -i https://pypi.tuna.tsinghua.edu.cn/simple/ black flake8"
2025-05-29 20:48:24,387 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple/
Requirement already satisfied: black in d:\program\conda\lib\site-packages (23.12.1)
Requirement already satisfied: flake8 in d:\program\conda\lib\site-packages (6.1.0)
Requirement already satisfied: click>=8.0.0 in d:\program\conda\lib\site-packages (from black) (8.2.0)
Requirement already satisfied: mypy-extensions>=0.4.3 in d:\program\conda\lib\site-packages (from black) (1.1.0)
Requirement already satisfied: packaging>=22.0 in d:\...
2025-05-29 20:48:24,387 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1389 - _execute_command_with_retry - ✅ 第 1 次尝试成功
2025-05-29 20:48:24,388 - bot_agent.engines.task_executor - INFO - task_executor.py:1747 - _execute_multi_round_intelligent_fix_with_logging - ✅ 步骤 1 执行成功
2025-05-29 20:48:24,390 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:241 - log_round - Logged round 6 for session task_1748522879_1748522879: 第6轮：修复步骤1 - 使用国内镜像源重试pip安装
2025-05-29 20:48:24,390 - bot_agent.engines.task_executor - INFO - task_executor.py:1724 - _execute_multi_round_intelligent_fix_with_logging - 🔧 执行修复步骤 2/2: 如果镜像源失败，尝试阿里云镜像
2025-05-29 20:48:24,390 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1336 - _execute_ai_fix_step - 🔧 执行AI修复步骤: 如果镜像源失败，尝试阿里云镜像
2025-05-29 20:48:24,390 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1337 - _execute_ai_fix_step - 📝 执行命令: pip install --timeout 60 --retries 3 -i https://mirrors.aliyun.com/pypi/simple/ black flake8
2025-05-29 20:48:24,391 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1368 - _execute_command_with_retry - 🔄 第 1 次尝试执行: pip install --timeout 60 --retries 3 -i https://mirrors.aliyun.com/pypi/simple/ black flake8
2025-05-29 20:48:28,203 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "pip install --timeout 60 --retries 3 -i https://mirrors.aliyun.com/pypi/simple/ black flake8"
2025-05-29 20:48:28,203 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: Looking in indexes: https://mirrors.aliyun.com/pypi/simple/
Requirement already satisfied: black in d:\program\conda\lib\site-packages (23.12.1)
Requirement already satisfied: flake8 in d:\program\conda\lib\site-packages (6.1.0)
Requirement already satisfied: click>=8.0.0 in d:\program\conda\lib\site-packages (from black) (8.2.0)
Requirement already satisfied: mypy-extensions>=0.4.3 in d:\program\conda\lib\site-packages (from black) (1.1.0)
Requirement already satisfied: packaging>=22.0 in d:\pr...
2025-05-29 20:48:28,204 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1389 - _execute_command_with_retry - ✅ 第 1 次尝试成功
2025-05-29 20:48:28,204 - bot_agent.engines.task_executor - INFO - task_executor.py:1747 - _execute_multi_round_intelligent_fix_with_logging - ✅ 步骤 2 执行成功
2025-05-29 20:48:28,206 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:241 - log_round - Logged round 7 for session task_1748522879_1748522879: 第7轮：修复步骤2 - 如果镜像源失败，尝试阿里云镜像
2025-05-29 20:48:28,207 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1697 - verify_fixes - ✅ 开始验证修复效果...
2025-05-29 20:48:31,918 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "black --check --config pyproject.toml ."
2025-05-29 20:48:34,886 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "flake8 ."
2025-05-29 20:48:34,887 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "E:\Projects\aider-plus\venv\Scripts\flake8.exe\__main__.py", line 7, in <module>
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\cli.py", line 23, in main
    app.run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 198, in run
    self._run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 186, in _run
    self.initialize(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 165, in initialize
    self.plugins, self.options = parse_args(argv)
                                 ^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\parse_args.py", line 53, in parse_args
    opts = aggregator.aggregate_options(option_manager, cfg, cfg_dir, rest)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\aggregator.py", line 30, in aggregate_options
    parsed_config = config.parse_config(manager, cfg, cfg_dir)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\config.py", line 131, in parse_config
    raise ValueError(
ValueError: Error code '#' supplied to 'extend-ignore' option does not match '^[A-Z]{1,3}[0-9]{0,3}$'

2025-05-29 20:48:37,197 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "python -m py_compile ""example.py"" ""setup.py"" ""api_proxy\config.py"" ""api_proxy\health_check.py"" ""api_proxy\job_analysis.py"" ""api_proxy\job_failure_analysis.py"" ""api_proxy\job_lint_analysis.py"" ""api_proxy\job_lint_service.py"" ""api_proxy\models.py"" ""api_proxy\monitoring.py"""
2025-05-29 20:48:37,197 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: [Errno 2] No such file or directory: 'example.py setup.py api_proxy\\config.py api_proxy\\health_check.py api_proxy\\job_analysis.py api_proxy\\job_failure_analysis.py api_proxy\\job_lint_analysis.py api_proxy\\job_lint_service.py api_proxy\\models.py api_proxy\\monitoring.py'
2025-05-29 20:48:37,198 - bot_agent.config.model_config - DEBUG - model_config.py:44 - get_analysis_model - 获取推理分析模型: openrouter/deepseek/deepseek-r1:free
2025-05-29 20:48:37,198 - bot_agent.config.model_config - DEBUG - model_config.py:84 - convert_aider_to_openrouter_format - 转换模型名称: openrouter/deepseek/deepseek-r1:free -> deepseek/deepseek-r1:free
2025-05-29 20:48:37,198 - bot_agent.config.model_config - INFO - model_config.py:141 - log_current_config - 当前AI模型配置:
2025-05-29 20:48:37,198 - bot_agent.config.model_config - DEBUG - model_config.py:44 - get_analysis_model - 获取推理分析模型: openrouter/deepseek/deepseek-r1:free
2025-05-29 20:48:37,198 - bot_agent.config.model_config - INFO - model_config.py:142 - log_current_config -   - 推理分析模型: openrouter/deepseek/deepseek-r1:free
2025-05-29 20:48:37,199 - bot_agent.config.model_config - DEBUG - model_config.py:56 - get_code_generation_model - 获取代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-29 20:48:37,199 - bot_agent.config.model_config - INFO - model_config.py:143 - log_current_config -   - 代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-29 20:48:37,199 - bot_agent.config.model_config - DEBUG - model_config.py:68 - get_chat_model - 获取通用对话模型: openrouter/deepseek/deepseek-chat
2025-05-29 20:48:37,199 - bot_agent.config.model_config - INFO - model_config.py:144 - log_current_config -   - 通用对话模型: openrouter/deepseek/deepseek-chat
2025-05-29 20:48:37,200 - bot_agent.config.model_config - INFO - model_config.py:145 - log_current_config -   - API密钥已设置: 是
2025-05-29 20:48:37,200 - bot_agent.config.model_config - DEBUG - model_config.py:44 - get_analysis_model - 获取推理分析模型: openrouter/deepseek/deepseek-r1:free
2025-05-29 20:48:37,200 - bot_agent.config.model_config - DEBUG - model_config.py:56 - get_code_generation_model - 获取代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-29 20:48:37,200 - bot_agent.config.model_config - INFO - model_config.py:135 - validate_config - 模型配置验证结果: {'api_key': True, 'analysis_model': True, 'code_generation_model': True}
2025-05-29 20:48:37,200 - bot_agent.config.model_config - INFO - model_config.py:150 - log_current_config - ✅ 所有模型配置正常
2025-05-29 20:48:37,200 - bot_agent.dispatcher.task_analyzer - INFO - task_analyzer.py:48 - __init__ - AI Task analyzer initialized with analysis model: deepseek/deepseek-r1:free
2025-05-29 20:49:06,676 - bot_agent.dispatcher.task_analyzer - INFO - task_analyzer.py:181 - _ai_analyze_task - AI分析响应: ```json
{
  "task_type": "project_analysis",
  "confidence": 0.8,
  "priority": "high",
  "complexity": "medium",
  "reasoning": "任务核心是对修复效果的验证分析而非直接执行修复，属于项目质量评估范畴。修复步骤显示100%执行成功但验证成功率0%存在矛盾，表明存在修复未生效或验证机制失效的严重问题。需要检查修复是否被正确应用、lint规则配置是否同步更新、验证环境是否污染等潜在因素。",
  "risks": [
    {
      "type": "functionality",
      "level": "high",
      "description": '修复代码可能未正确合并或部署，导致实际未生效'
    },
    {
      "type": "configuration",
      "level": "medium",
      "description": "lint规则配置与验证标准可能存在版本不一致"
    },
    {
      "type": "environment",
      "level": "medium",
      "description": "验证环境存在缓存污染或依赖项版本冲突"
    }
  ]
}
```
2025-05-29 20:49:06,677 - bot_agent.dispatcher.task_analyzer - WARNING - task_analyzer.py:201 - _ai_analyze_task - AI响应不是标准JSON，尝试解析: ```json
{
  "task_type": "project_analysis",
  "confidence": 0.8,
  "priority": "high",
  "complexity": "medium",
  "reasoning": "任务核心是对修复效果的验证分析而非直接执行修复，属于项目质量评估范畴。修复步骤显示100%执行成功但验证成功率0%存在矛盾，表明存在修复未生效或验证机制失效的严重问题。需要检查修复是否被正确应用、lint规则配置是否同步更新、验证环境是否污染等潜在因素。",
  "risks": [
    {
      "type": "functionality",
      "level": "high",
      "description": '修复代码可能未正确合并或部署，导致实际未生效'
    },
    {
      "type": "configuration",
      "level": "medium",
      "description": "lint规则配置与验证标准可能存在版本不一致"
    },
    {
      "type": "environment",
      "level": "medium",
      "description": "验证环境存在缓存污染或依赖项版本冲突"
    }
  ]
}
```
2025-05-29 20:49:06,679 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:241 - log_round - Logged round 8 for session task_1748522879_1748522879: 第8轮：修复效果验证
2025-05-29 20:49:06,679 - bot_agent.engines.task_executor - INFO - task_executor.py:1840 - _evaluate_fix_completeness - 🔍 验证完全失败，需要额外修复
2025-05-29 20:49:06,680 - bot_agent.engines.task_executor - WARNING - task_executor.py:1399 - _handle_job_failure_analysis - 🔄 检测到修复不完整，启动AI驱动的多轮修复...
2025-05-29 20:49:06,680 - bot_agent.engines.task_executor - INFO - task_executor.py:1926 - _execute_ai_driven_multi_round_fix - 🤖 启动AI驱动的多轮修复...
2025-05-29 20:49:06,680 - bot_agent.engines.task_executor - INFO - task_executor.py:1937 - _execute_ai_driven_multi_round_fix - 🎯 发现 2 个剩余错误，开始AI驱动修复
2025-05-29 20:49:06,681 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:189 - execute_targeted_fixes - 🤖 开始AI驱动的智能修复...
2025-05-29 20:49:06,681 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:519 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 20:49:06,681 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:524 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 20:49:06,681 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\aider-git-repos\ai-proxy
2025-05-29 20:49:10,143 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 20:49:10,143 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 20:49:12,455 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 20:49:12,456 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 20:49:12,456 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 20:49:12,458 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:241 - log_round - Logged round 9 for session task_1748522879_1748522879: 第9轮：AI重试1: provider_error
2025-05-29 20:49:12,458 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 1): cannot access free variable 'ai_prompt' where it is not associated with a value in enclosing scope
2025-05-29 20:49:12,458 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 1.1 秒后重试...
2025-05-29 20:49:13,620 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:241 - log_round - Logged round 10 for session task_1748522879_1748522879: 第10轮：AI重试2: provider_error
2025-05-29 20:49:13,621 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 2): cannot access free variable 'ai_prompt' where it is not associated with a value in enclosing scope
2025-05-29 20:49:13,621 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 2.5 秒后重试...
2025-05-29 20:49:16,164 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:241 - log_round - Logged round 11 for session task_1748522879_1748522879: 第11轮：AI重试3: provider_error
2025-05-29 20:49:16,164 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 3): cannot access free variable 'ai_prompt' where it is not associated with a value in enclosing scope
2025-05-29 20:49:16,164 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 5.2 秒后重试...
2025-05-29 20:49:21,355 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:160 - execute_with_retry - 达到最大重试次数，使用降级策略
2025-05-29 20:49:21,356 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:743 - _ai_generate_fix_plan - AI响应JSON解析失败: Expecting value: line 2 column 1 (char 1)
2025-05-29 20:49:21,357 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:750 - _ai_generate_fix_plan - 文本解析也失败，使用智能fallback策略
2025-05-29 20:49:21,357 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:831 - _create_intelligent_fallback_plan_async - 🧠 创建智能fallback修复计划...
2025-05-29 20:49:21,358 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:835 - _create_intelligent_fallback_plan_async - 🔍 原始日志长度: 0
2025-05-29 20:49:21,358 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:837 - _create_intelligent_fallback_plan_async - 🎯 识别的错误模式: {'network_timeout': [{'type': 'network_timeout_error', 'severity': 'medium', 'category': 'network', 'line_number': 73, 'file_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp4i5ycg5t.log', 'content': 'socket.timeout: The read operation timed out', 'solutions': ['检查网络连接', '使用国内镜像源', '增加超时时间', '重试下载操作'], 'timestamp': None}, {'type': 'network_timeout_error', 'severity': 'medium', 'category': 'network', 'line_number': 135, 'file_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp4i5ycg5t.log', 'content': 'raise ReadTimeoutError(self._pool, None, "Read timed out.")', 'solutions': ['检查网络连接', '使用国内镜像源', '增加超时时间', '重试下载操作'], 'timestamp': None}, {'type': 'network_timeout_error', 'severity': 'medium', 'category': 'network', 'line_number': 136, 'file_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp4i5ycg5t.log', 'content': "pip._vendor.urllib3.exceptions.ReadTimeoutError: HTTPSConnectionPool(host='files.pythonhosted.org', port=443): Read timed out.", 'solutions': ['检查网络连接', '使用国内镜像源', '增加超时时间', '重试下载操作'], 'timestamp': None}]}
2025-05-29 20:49:21,359 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:220 - execute_targeted_fixes - 🔧 执行修复步骤 1/2: 使用国内镜像源重试pip安装
2025-05-29 20:49:21,360 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1336 - _execute_ai_fix_step - 🔧 执行AI修复步骤: 使用国内镜像源重试pip安装
2025-05-29 20:49:21,360 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1337 - _execute_ai_fix_step - 📝 执行命令: pip install --timeout 60 --retries 3 -i https://pypi.tuna.tsinghua.edu.cn/simple/ black flake8
2025-05-29 20:49:21,360 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1368 - _execute_command_with_retry - 🔄 第 1 次尝试执行: pip install --timeout 60 --retries 3 -i https://pypi.tuna.tsinghua.edu.cn/simple/ black flake8
2025-05-29 20:49:25,878 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "pip install --timeout 60 --retries 3 -i https://pypi.tuna.tsinghua.edu.cn/simple/ black flake8"
2025-05-29 20:49:25,878 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple/
Requirement already satisfied: black in d:\program\conda\lib\site-packages (23.12.1)
Requirement already satisfied: flake8 in d:\program\conda\lib\site-packages (6.1.0)
Requirement already satisfied: click>=8.0.0 in d:\program\conda\lib\site-packages (from black) (8.2.0)
Requirement already satisfied: mypy-extensions>=0.4.3 in d:\program\conda\lib\site-packages (from black) (1.1.0)
Requirement already satisfied: packaging>=22.0 in d:\...
2025-05-29 20:49:25,879 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1389 - _execute_command_with_retry - ✅ 第 1 次尝试成功
2025-05-29 20:49:25,879 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:220 - execute_targeted_fixes - 🔧 执行修复步骤 2/2: 如果镜像源失败，尝试阿里云镜像
2025-05-29 20:49:25,880 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1336 - _execute_ai_fix_step - 🔧 执行AI修复步骤: 如果镜像源失败，尝试阿里云镜像
2025-05-29 20:49:25,881 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1337 - _execute_ai_fix_step - 📝 执行命令: pip install --timeout 60 --retries 3 -i https://mirrors.aliyun.com/pypi/simple/ black flake8
2025-05-29 20:49:25,881 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1368 - _execute_command_with_retry - 🔄 第 1 次尝试执行: pip install --timeout 60 --retries 3 -i https://mirrors.aliyun.com/pypi/simple/ black flake8
2025-05-29 20:49:30,223 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "pip install --timeout 60 --retries 3 -i https://mirrors.aliyun.com/pypi/simple/ black flake8"
2025-05-29 20:49:30,224 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: Looking in indexes: https://mirrors.aliyun.com/pypi/simple/
Requirement already satisfied: black in d:\program\conda\lib\site-packages (23.12.1)
Requirement already satisfied: flake8 in d:\program\conda\lib\site-packages (6.1.0)
Requirement already satisfied: click>=8.0.0 in d:\program\conda\lib\site-packages (from black) (8.2.0)
Requirement already satisfied: mypy-extensions>=0.4.3 in d:\program\conda\lib\site-packages (from black) (1.1.0)
Requirement already satisfied: packaging>=22.0 in d:\pr...
2025-05-29 20:49:30,224 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1389 - _execute_command_with_retry - ✅ 第 1 次尝试成功
2025-05-29 20:49:30,228 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:241 - log_round - Logged round 12 for session task_1748522879_1748522879: 第12轮：AI驱动的多轮修复
2025-05-29 20:49:30,228 - bot_agent.engines.task_executor - INFO - task_executor.py:1413 - _handle_job_failure_analysis - ✅ AI驱动的多轮修复成功
2025-05-29 20:49:30,229 - bot_agent.engines.task_executor - WARNING - task_executor.py:1420 - _handle_job_failure_analysis - 🔄 验证失败或成功率过低，启动第二轮智能修复...
2025-05-29 20:49:30,229 - bot_agent.engines.task_executor - INFO - task_executor.py:2126 - _execute_second_round_fix - 🔄 启动第二轮智能修复...
2025-05-29 20:49:30,230 - bot_agent.engines.task_executor - INFO - task_executor.py:2140 - _execute_second_round_fix - 🤖 使用AI分析验证失败原因...
2025-05-29 20:49:30,231 - bot_agent.engines.task_executor - ERROR - task_executor.py:2167 - _execute_second_round_fix - AI分析验证失败原因异常: 'IntelligentToolCoordinator' object has no attribute '_ai_analyze_verification_failures'
2025-05-29 20:49:30,231 - bot_agent.engines.task_executor - INFO - task_executor.py:2172 - _execute_second_round_fix - 🎯 第二轮修复目标：6 个剩余错误
2025-05-29 20:49:30,232 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:189 - execute_targeted_fixes - 🤖 开始AI驱动的智能修复...
2025-05-29 20:49:30,237 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:241 - log_round - Logged round 13 for session task_1748522879_1748522879: 第13轮：第二轮精准修复1: {'type': 'network_timeout_error', 'severity': 'med
2025-05-29 20:49:30,238 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:189 - execute_targeted_fixes - 🤖 开始AI驱动的智能修复...
2025-05-29 20:49:30,241 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:241 - log_round - Logged round 14 for session task_1748522879_1748522879: 第14轮：第二轮精准修复2: {'type': 'network_timeout_error', 'severity': 'med
2025-05-29 20:49:30,242 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:189 - execute_targeted_fixes - 🤖 开始AI驱动的智能修复...
2025-05-29 20:49:30,245 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:241 - log_round - Logged round 15 for session task_1748522879_1748522879: 第15轮：第二轮精准修复3: {'type': 'network_timeout_error', 'severity': 'med
2025-05-29 20:49:30,246 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:189 - execute_targeted_fixes - 🤖 开始AI驱动的智能修复...
2025-05-29 20:49:30,251 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:241 - log_round - Logged round 16 for session task_1748522879_1748522879: 第16轮：第二轮精准修复4: {'type': 'generic_job_failure', 'severity': 'mediu
2025-05-29 20:49:30,251 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:189 - execute_targeted_fixes - 🤖 开始AI驱动的智能修复...
2025-05-29 20:49:30,256 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:241 - log_round - Logged round 17 for session task_1748522879_1748522879: 第17轮：第二轮精准修复5: Traceback (most recent call last):
2025-05-29 20:49:30,257 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:189 - execute_targeted_fixes - 🤖 开始AI驱动的智能修复...
2025-05-29 20:49:30,261 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:241 - log_round - Logged round 18 for session task_1748522879_1748522879: 第18轮：第二轮精准修复6: self.gen.throw(typ, value, traceback)
2025-05-29 20:49:30,266 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:241 - log_round - Logged round 19 for session task_1748522879_1748522879: 第19轮：第二轮智能修复
2025-05-29 20:49:30,267 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - AI智能分析完成
2025-05-29 20:49:30,267 - bot_agent.engines.task_executor - INFO - task_executor.py:1516 - _handle_job_failure_analysis - ✅ AI智能分析完成，保留原始AI响应
2025-05-29 20:49:30,272 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:277 - end_session - Ended conversation session: task_1748522879_1748522879, status: ConversationStatus.SUCCESS
2025-05-29 20:49:30,273 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - ✅ 注销监控点: job_failure_analysis，执行时间: 90.95s
2025-05-29 20:49:31,210 - bot_agent.engines.task_executor - INFO - task_executor.py:316 - _execute_with_aider - 代码已提交: AI自动修改: 作业失败分析 - lint (Job 944)
2025-05-29 20:49:42,131 - bot_agent.engines.task_executor - INFO - task_executor.py:342 - _execute_with_aider - 代码已推送到远程分支: aider-plus-dev
2025-05-29 20:49:42,134 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:62 - analyze_task_completion - Analyzed task completion: 7 global, 1 project memories
2025-05-29 20:49:42,136 - bot_agent.memory.global_memory - INFO - global_memory.py:90 - save_work_habit - Work habit saved: task_preference
2025-05-29 20:49:42,147 - bot_agent.memory.global_memory - INFO - global_memory.py:90 - save_work_habit - Work habit saved: tool_preference
2025-05-29 20:49:42,149 - bot_agent.memory.global_memory - INFO - global_memory.py:136 - save_common_pattern - Common pattern saved: file_creation
2025-05-29 20:49:42,158 - bot_agent.memory.global_memory - INFO - global_memory.py:136 - save_common_pattern - Common pattern saved: directory_structure
2025-05-29 20:49:42,160 - bot_agent.memory.global_memory - INFO - global_memory.py:109 - save_preference - Preference saved: programming_languages
2025-05-29 20:49:42,173 - bot_agent.memory.global_memory - INFO - global_memory.py:109 - save_preference - Preference saved: programming_languages
2025-05-29 20:49:42,183 - bot_agent.memory.global_memory - INFO - global_memory.py:109 - save_preference - Preference saved: programming_languages
2025-05-29 20:49:42,186 - bot_agent.memory.project_memory - INFO - project_memory.py:94 - save_coding_standard - Coding standard saved: general
2025-05-29 20:49:42,187 - bot_agent.memory.global_memory - INFO - global_memory.py:150 - save_environment_info - Environment info saved
2025-05-29 20:49:42,188 - bot_agent.memory.memory_integration - INFO - memory_integration.py:111 - learn_from_task_completion - Learned from task completion: 7 global, 1 project memories
2025-05-29 20:49:42,188 - bot_agent.handlers.ai_response_handler - WARNING - ai_response_handler.py:104 - _handle_gitlab_response - Unsupported event type: Job Hook
2025-05-29 20:49:42,189 - bot_agent.dispatcher.router - INFO - router.py:111 - _dispatch_to_component - Task b4a0a659-a273-4a12-82d2-3126a57375b4 processed by AI processor: success
2025-05-29 20:49:42,189 - bot_agent.webhook.gitlab - INFO - gitlab.py:1237 - handle_job_event - Job event task routed: {'task_id': 'b4a0a659-a273-4a12-82d2-3126a57375b4', 'target_component': 'aider', 'status': 'accepted', 'message': 'Task b4a0a659-a273-4a12-82d2-3126a57375b4 accepted and processed'}
2025-05-29 20:49:42,190 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'success', 'message': 'Job lint (failed) routed to AI for critical_job_failure', 'task_id': 'b4a0a659-a273-4a12-82d2-3126a57375b4', 'processing_reason': 'critical_job_failure'}
2025-05-29 20:49:44,754 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-29 20:49:44,754 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-29 20:49:44,755 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-29 20:49:44,755 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '13551fad-28bf-4fee-b153-eceb90c96c9a', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '89e22fef-13a4-4934-8fe2-02984087ba54', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'bb1b92ec-8a80-**********************', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1935'}
2025-05-29 20:49:44,756 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-29 20:49:44,756 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-29 20:49:44,757 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-29 20:49:44,757 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-29 20:49:44,758 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '13551fad-28bf-4fee-b153-eceb90c96c9a', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '89e22fef-13a4-4934-8fe2-02984087ba54', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'bb1b92ec-8a80-**********************', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1935'}
2025-05-29 20:49:44,758 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': '3c0e39a02aa5e03f59c9d410316f14eb6a426cd2', 'sha': '32c1d1bfb45324a0917d45d0089230e61e8d3cc3', 'retries_count': 0, 'build_id': 945, 'build_name': 'test', 'build_stage': 'test', 'build_status': 'created', 'build_created_at': '2025-05-29 12:49:41 UTC', 'build_started_at': None, 'build_finished_at': None, 'build_created_at_iso': '2025-05-29T12:49:41Z', 'build_started_at_iso': None, 'build_finished_at_iso': None, 'build_duration': None, 'build_queued_duration': None, 'build_allow_failure': False, 'build_failure_reason': 'unknown_failure', 'pipeline_id': 255, 'runner': None, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 255, 'name': None, 'sha': '32c1d1bfb45324a0917d45d0089230e61e8d3cc3', 'message': 'AI自动修改: 作业失败分析 - lint (Job 944)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'created', 'duration': None, 'started_at': None, 'finished_at': None, 'started_at_iso': None, 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-29 20:49:44,760 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-29 20:49:44,760 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-29 20:49:44,761 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job test (945) in stage test is created (Pipeline: 255, Project: ai-proxy, User: Longer)
2025-05-29 20:49:44,761 - bot_agent.webhook.gitlab - INFO - gitlab.py:1246 - handle_job_event - Job test status created recorded (no AI processing needed)
2025-05-29 20:49:44,762 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Job test status created recorded'}
2025-05-29 20:49:45,386 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-29 20:49:45,387 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-29 20:49:45,388 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-29 20:49:45,388 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '52903f92-ce35-43b6-b4a6-56648b7ea9fc', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': 'c4d4a84d-a24c-43b3-b0f3-22f845c010a7', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '97ca49f6-c85e-4ada-85de-b89d74f5bcf5', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1935'}
2025-05-29 20:49:45,389 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-29 20:49:45,389 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-29 20:49:45,390 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-29 20:49:45,390 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-29 20:49:45,390 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '52903f92-ce35-43b6-b4a6-56648b7ea9fc', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': 'c4d4a84d-a24c-43b3-b0f3-22f845c010a7', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '97ca49f6-c85e-4ada-85de-b89d74f5bcf5', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1935'}
2025-05-29 20:49:45,391 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': '3c0e39a02aa5e03f59c9d410316f14eb6a426cd2', 'sha': '32c1d1bfb45324a0917d45d0089230e61e8d3cc3', 'retries_count': 0, 'build_id': 946, 'build_name': 'lint', 'build_stage': 'test', 'build_status': 'created', 'build_created_at': '2025-05-29 12:49:41 UTC', 'build_started_at': None, 'build_finished_at': None, 'build_created_at_iso': '2025-05-29T12:49:41Z', 'build_started_at_iso': None, 'build_finished_at_iso': None, 'build_duration': None, 'build_queued_duration': None, 'build_allow_failure': False, 'build_failure_reason': 'unknown_failure', 'pipeline_id': 255, 'runner': None, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 255, 'name': None, 'sha': '32c1d1bfb45324a0917d45d0089230e61e8d3cc3', 'message': 'AI自动修改: 作业失败分析 - lint (Job 944)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'created', 'duration': None, 'started_at': None, 'finished_at': None, 'started_at_iso': None, 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-29 20:49:45,393 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-29 20:49:45,393 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-29 20:49:45,394 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job lint (946) in stage test is created (Pipeline: 255, Project: ai-proxy, User: Longer)
2025-05-29 20:49:45,394 - bot_agent.webhook.gitlab - INFO - gitlab.py:1246 - handle_job_event - Job lint status created recorded (no AI processing needed)
2025-05-29 20:49:45,395 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Job lint status created recorded'}
2025-05-29 20:49:46,133 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-29 20:49:46,134 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-29 20:49:46,134 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-29 20:49:46,134 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'db21bee1-3661-47a0-99b0-81d37baca170', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': 'd9d3e1f4-04c3-446c-8c3a-72a5dec1f8ab', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'bb0ea9f6-d490-441a-a8ae-53453118e7b3', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1937'}
2025-05-29 20:49:46,135 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-29 20:49:46,135 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-29 20:49:46,136 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-29 20:49:46,136 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-29 20:49:46,137 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'db21bee1-3661-47a0-99b0-81d37baca170', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': 'd9d3e1f4-04c3-446c-8c3a-72a5dec1f8ab', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'bb0ea9f6-d490-441a-a8ae-53453118e7b3', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1937'}
2025-05-29 20:49:46,137 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': '3c0e39a02aa5e03f59c9d410316f14eb6a426cd2', 'sha': '32c1d1bfb45324a0917d45d0089230e61e8d3cc3', 'retries_count': 0, 'build_id': 947, 'build_name': 'build', 'build_stage': 'build', 'build_status': 'created', 'build_created_at': '2025-05-29 12:49:41 UTC', 'build_started_at': None, 'build_finished_at': None, 'build_created_at_iso': '2025-05-29T12:49:41Z', 'build_started_at_iso': None, 'build_finished_at_iso': None, 'build_duration': None, 'build_queued_duration': None, 'build_allow_failure': False, 'build_failure_reason': 'unknown_failure', 'pipeline_id': 255, 'runner': None, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 255, 'name': None, 'sha': '32c1d1bfb45324a0917d45d0089230e61e8d3cc3', 'message': 'AI自动修改: 作业失败分析 - lint (Job 944)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'created', 'duration': None, 'started_at': None, 'finished_at': None, 'started_at_iso': None, 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-29 20:49:46,139 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-29 20:49:46,139 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-29 20:49:46,140 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job build (947) in stage build is created (Pipeline: 255, Project: ai-proxy, User: Longer)
2025-05-29 20:49:46,140 - bot_agent.webhook.gitlab - INFO - gitlab.py:1246 - handle_job_event - Job build status created recorded (no AI processing needed)
2025-05-29 20:49:46,141 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Job build status created recorded'}
2025-05-29 20:49:49,811 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-29 20:49:49,811 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-29 20:49:49,812 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-29 20:49:49,812 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '44d1299f-9b80-4c62-a415-e643b15ac451', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '7f88aaea-7a35-454d-98e9-ebfae85b264e', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '0a202021-9d6e-4e29-a427-005e6e94b6f1', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1942'}
2025-05-29 20:49:49,813 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-29 20:49:49,813 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-29 20:49:49,814 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-29 20:49:49,814 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-29 20:49:49,815 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '44d1299f-9b80-4c62-a415-e643b15ac451', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '7f88aaea-7a35-454d-98e9-ebfae85b264e', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '0a202021-9d6e-4e29-a427-005e6e94b6f1', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1942'}
2025-05-29 20:49:49,815 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': '3c0e39a02aa5e03f59c9d410316f14eb6a426cd2', 'sha': '32c1d1bfb45324a0917d45d0089230e61e8d3cc3', 'retries_count': 0, 'build_id': 945, 'build_name': 'test', 'build_stage': 'test', 'build_status': 'pending', 'build_created_at': '2025-05-29 12:49:41 UTC', 'build_started_at': None, 'build_finished_at': None, 'build_created_at_iso': '2025-05-29T12:49:41Z', 'build_started_at_iso': None, 'build_finished_at_iso': None, 'build_duration': None, 'build_queued_duration': 0.230443895, 'build_allow_failure': False, 'build_failure_reason': 'unknown_failure', 'pipeline_id': 255, 'runner': None, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 255, 'name': None, 'sha': '32c1d1bfb45324a0917d45d0089230e61e8d3cc3', 'message': 'AI自动修改: 作业失败分析 - lint (Job 944)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'created', 'duration': None, 'started_at': None, 'finished_at': None, 'started_at_iso': None, 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-29 20:49:49,817 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-29 20:49:49,817 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-29 20:49:49,818 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job test (945) in stage test is pending (Pipeline: 255, Project: ai-proxy, User: Longer)
2025-05-29 20:49:49,818 - bot_agent.webhook.gitlab - INFO - gitlab.py:1246 - handle_job_event - Job test status pending recorded (no AI processing needed)
2025-05-29 20:49:49,819 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Job test status pending recorded'}
2025-05-29 20:49:50,145 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-29 20:49:50,145 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-29 20:49:50,146 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-29 20:49:50,146 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '2bc20e72-50e5-4940-ad8d-e303efcdebe0', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '6588f559-e49f-4903-b319-69ac1738a246', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'da916521-746d-**********************', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1942'}
2025-05-29 20:49:50,147 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-29 20:49:50,147 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-29 20:49:50,148 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-29 20:49:50,148 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-29 20:49:50,148 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '2bc20e72-50e5-4940-ad8d-e303efcdebe0', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '6588f559-e49f-4903-b319-69ac1738a246', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'da916521-746d-**********************', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1942'}
2025-05-29 20:49:50,149 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': '3c0e39a02aa5e03f59c9d410316f14eb6a426cd2', 'sha': '32c1d1bfb45324a0917d45d0089230e61e8d3cc3', 'retries_count': 0, 'build_id': 946, 'build_name': 'lint', 'build_stage': 'test', 'build_status': 'pending', 'build_created_at': '2025-05-29 12:49:41 UTC', 'build_started_at': None, 'build_finished_at': None, 'build_created_at_iso': '2025-05-29T12:49:41Z', 'build_started_at_iso': None, 'build_finished_at_iso': None, 'build_duration': None, 'build_queued_duration': 0.279170946, 'build_allow_failure': False, 'build_failure_reason': 'unknown_failure', 'pipeline_id': 255, 'runner': None, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 255, 'name': None, 'sha': '32c1d1bfb45324a0917d45d0089230e61e8d3cc3', 'message': 'AI自动修改: 作业失败分析 - lint (Job 944)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'created', 'duration': None, 'started_at': None, 'finished_at': None, 'started_at_iso': None, 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-29 20:49:50,151 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-29 20:49:50,151 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-29 20:49:50,152 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job lint (946) in stage test is pending (Pipeline: 255, Project: ai-proxy, User: Longer)
2025-05-29 20:49:50,152 - bot_agent.webhook.gitlab - INFO - gitlab.py:1246 - handle_job_event - Job lint status pending recorded (no AI processing needed)
2025-05-29 20:49:50,153 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Job lint status pending recorded'}
2025-05-29 20:49:51,024 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-29 20:49:51,025 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-29 20:49:51,025 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-29 20:49:51,026 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '0beb9c61-880e-4e7f-af45-88917f7a2d96', 'x-gitlab-event': 'Pipeline Hook', 'x-gitlab-webhook-uuid': '4fad1fe6-6a8e-4aa4-9078-462fe7e41284', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'b2e6ce09-0e86-4f0c-8f9f-7b30537c3458', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '3102'}
2025-05-29 20:49:51,027 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-29 20:49:51,027 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-29 20:49:51,027 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-29 20:49:51,028 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-29 20:49:51,028 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '0beb9c61-880e-4e7f-af45-88917f7a2d96', 'x-gitlab-event': 'Pipeline Hook', 'x-gitlab-webhook-uuid': '4fad1fe6-6a8e-4aa4-9078-462fe7e41284', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'b2e6ce09-0e86-4f0c-8f9f-7b30537c3458', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '3102'}
2025-05-29 20:49:51,029 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'pipeline', 'object_attributes': {'id': 255, 'iid': 77, 'name': None, 'ref': 'aider-plus-dev', 'tag': False, 'sha': '32c1d1bfb45324a0917d45d0089230e61e8d3cc3', 'before_sha': '3c0e39a02aa5e03f59c9d410316f14eb6a426cd2', 'source': 'push', 'status': 'pending', 'detailed_status': 'pending', 'stages': ['test', 'build'], 'created_at': '2025-05-29 12:49:41 UTC', 'finished_at': None, 'duration': None, 'queued_duration': None, 'variables': [], 'url': 'http://***************/Longer/ai-proxy/-/pipelines/255'}, 'merge_request': None, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'commit': {'id': '32c1d1bfb45324a0917d45d0089230e61e8d3cc3', 'message': 'AI自动修改: 作业失败分析 - lint (Job 944)\n', 'title': 'AI自动修改: 作业失败分析 - lint (Job 944)', 'timestamp': '2025-05-29T20:49:30+08:00', 'url': 'http://***************/Longer/ai-proxy/-/commit/32c1d1bfb45324a0917d45d0089230e61e8d3cc3', 'author': {'name': 'Longer (aider)', 'email': '<EMAIL>'}}, 'builds': [{'id': 947, 'stage': 'build', 'name': 'build', 'status': 'created', 'created_at': '2025-05-29 12:49:41 UTC', 'started_at': None, 'finished_at': None, 'duration': None, 'queued_duration': None, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': None, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}, {'id': 946, 'stage': 'test', 'name': 'lint', 'status': 'pending', 'created_at': '2025-05-29 12:49:41 UTC', 'started_at': None, 'finished_at': None, 'duration': None, 'queued_duration': 1.688719782, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': None, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}, {'id': 945, 'stage': 'test', 'name': 'test', 'status': 'pending', 'created_at': '2025-05-29 12:49:41 UTC', 'started_at': None, 'finished_at': None, 'duration': None, 'queued_duration': 2.269408725, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': None, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}]}
2025-05-29 20:49:51,032 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Pipeline Hook
2025-05-29 20:49:51,032 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Pipeline Hook, object_kind: pipeline, action: unknown
2025-05-29 20:49:51,033 - bot_agent.webhook.gitlab - INFO - gitlab.py:950 - handle_pipeline_event - Processing Pipeline event: Pipeline 255 for aider-plus-dev is pending (Project: ai-proxy, User: Longer)
2025-05-29 20:49:51,033 - bot_agent.webhook.gitlab - INFO - gitlab.py:1082 - handle_pipeline_event - Pipeline 255 status pending recorded (no AI monitoring needed)
2025-05-29 20:49:51,034 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Pipeline 255 status pending recorded'}
2025-05-29 20:49:52,483 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-29 20:49:52,484 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-29 20:49:52,485 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-29 20:49:52,485 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '10fb4107-e961-4271-8274-0d3d7c414e19', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': 'e2fb49be-528f-492b-9a7a-28bef6a11ede', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'bc65c89c-19a4-42a6-884f-c1e7822b6401', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '2105'}
2025-05-29 20:49:52,486 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-29 20:49:52,486 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-29 20:49:52,486 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-29 20:49:52,487 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-29 20:49:52,487 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '10fb4107-e961-4271-8274-0d3d7c414e19', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': 'e2fb49be-528f-492b-9a7a-28bef6a11ede', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'bc65c89c-19a4-42a6-884f-c1e7822b6401', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '2105'}
2025-05-29 20:49:52,488 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': '3c0e39a02aa5e03f59c9d410316f14eb6a426cd2', 'sha': '32c1d1bfb45324a0917d45d0089230e61e8d3cc3', 'retries_count': 0, 'build_id': 945, 'build_name': 'test', 'build_stage': 'test', 'build_status': 'running', 'build_created_at': '2025-05-29 12:49:41 UTC', 'build_started_at': '2025-05-29 12:49:50 UTC', 'build_finished_at': None, 'build_created_at_iso': '2025-05-29T12:49:41Z', 'build_started_at_iso': '2025-05-29T12:49:50Z', 'build_finished_at_iso': None, 'build_duration': 0.18768996, 'build_queued_duration': 3.363643054, 'build_allow_failure': False, 'build_failure_reason': 'unknown_failure', 'pipeline_id': 255, 'runner': {'id': 1, 'description': 'docker-runner-137', 'runner_type': 'instance_type', 'active': True, 'is_shared': True, 'tags': ['Dev-137']}, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 255, 'name': None, 'sha': '32c1d1bfb45324a0917d45d0089230e61e8d3cc3', 'message': 'AI自动修改: 作业失败分析 - lint (Job 944)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'pending', 'duration': None, 'started_at': None, 'finished_at': None, 'started_at_iso': None, 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-29 20:49:52,490 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-29 20:49:52,490 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-29 20:49:52,491 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job test (945) in stage test is running (Pipeline: 255, Project: ai-proxy, User: Longer)
2025-05-29 20:49:52,491 - bot_agent.webhook.gitlab - INFO - gitlab.py:1246 - handle_job_event - Job test status running recorded (no AI processing needed)
2025-05-29 20:49:52,491 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Job test status running recorded'}
2025-05-29 20:49:53,702 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-29 20:49:53,703 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-29 20:49:53,703 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-29 20:49:53,704 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '04067672-9e7d-41ef-b003-f301063498ac', 'x-gitlab-event': 'Pipeline Hook', 'x-gitlab-webhook-uuid': '4c14953f-e128-4ef2-bc8c-241dbf242e2b', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'ce156524-565e-4288-ad5b-0408884d7992', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '3241'}
2025-05-29 20:49:53,704 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-29 20:49:53,705 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-29 20:49:53,705 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-29 20:49:53,706 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-29 20:49:53,706 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '04067672-9e7d-41ef-b003-f301063498ac', 'x-gitlab-event': 'Pipeline Hook', 'x-gitlab-webhook-uuid': '4c14953f-e128-4ef2-bc8c-241dbf242e2b', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'ce156524-565e-4288-ad5b-0408884d7992', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '3241'}
2025-05-29 20:49:53,707 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'pipeline', 'object_attributes': {'id': 255, 'iid': 77, 'name': None, 'ref': 'aider-plus-dev', 'tag': False, 'sha': '32c1d1bfb45324a0917d45d0089230e61e8d3cc3', 'before_sha': '3c0e39a02aa5e03f59c9d410316f14eb6a426cd2', 'source': 'push', 'status': 'running', 'detailed_status': 'running', 'stages': ['test', 'build'], 'created_at': '2025-05-29 12:49:41 UTC', 'finished_at': None, 'duration': None, 'queued_duration': 9, 'variables': [], 'url': 'http://***************/Longer/ai-proxy/-/pipelines/255'}, 'merge_request': None, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'commit': {'id': '32c1d1bfb45324a0917d45d0089230e61e8d3cc3', 'message': 'AI自动修改: 作业失败分析 - lint (Job 944)\n', 'title': 'AI自动修改: 作业失败分析 - lint (Job 944)', 'timestamp': '2025-05-29T20:49:30+08:00', 'url': 'http://***************/Longer/ai-proxy/-/commit/32c1d1bfb45324a0917d45d0089230e61e8d3cc3', 'author': {'name': 'Longer (aider)', 'email': '<EMAIL>'}}, 'builds': [{'id': 947, 'stage': 'build', 'name': 'build', 'status': 'created', 'created_at': '2025-05-29 12:49:41 UTC', 'started_at': None, 'finished_at': None, 'duration': None, 'queued_duration': None, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': None, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}, {'id': 946, 'stage': 'test', 'name': 'lint', 'status': 'pending', 'created_at': '2025-05-29 12:49:41 UTC', 'started_at': None, 'finished_at': None, 'duration': None, 'queued_duration': 4.398416466, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': None, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}, {'id': 945, 'stage': 'test', 'name': 'test', 'status': 'running', 'created_at': '2025-05-29 12:49:41 UTC', 'started_at': '2025-05-29 12:49:50 UTC', 'finished_at': None, 'duration': 1.61866085, 'queued_duration': 3.363643, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': {'id': 1, 'description': 'docker-runner-137', 'runner_type': 'instance_type', 'active': True, 'is_shared': True, 'tags': ['Dev-137']}, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}]}
2025-05-29 20:49:53,709 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Pipeline Hook
2025-05-29 20:49:53,709 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Pipeline Hook, object_kind: pipeline, action: unknown
2025-05-29 20:49:53,710 - bot_agent.webhook.gitlab - INFO - gitlab.py:950 - handle_pipeline_event - Processing Pipeline event: Pipeline 255 for aider-plus-dev is running (Project: ai-proxy, User: Longer)
2025-05-29 20:49:53,710 - bot_agent.webhook.gitlab - INFO - gitlab.py:1082 - handle_pipeline_event - Pipeline 255 status running recorded (no AI monitoring needed)
2025-05-29 20:49:53,711 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Pipeline 255 status running recorded'}
2025-05-29 20:50:52,064 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 📊 当前监控状态:
2025-05-29 20:50:52,065 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log -    无活跃监控点
