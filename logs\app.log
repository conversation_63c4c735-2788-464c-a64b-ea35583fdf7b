2025-05-29 20:47:34,415 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 20:47:34,415 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 20:47:34,416 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 20:47:34,417 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-29 20:47:34,417 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-29 20:47:34,419 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://192.168.123.103/api/v4
2025-05-29 20:47:34,420 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://192.168.123.103/api/v4/version
2025-05-29 20:47:35,535 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:89 - _check_connection - Successfully connected to GitLab API: {'version': '17.11.1-ee', 'revision': 'cd4c186e8bc', 'kas': {'enabled': True, 'externalUrl': 'ws://192.168.123.103/-/kubernetes-agent/', 'externalK8sProxyUrl': 'http://192.168.123.103/-/kubernetes-agent/k8s-proxy/', 'version': '17.11.1'}, 'enterprise': True}
2025-05-29 20:47:35,536 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-29 20:47:35,536 - bot_agent.handlers.ai_processor - INFO - ai_processor.py:136 - _process_with_aider - 使用AiderBasedTaskExecutor处理任务: 作业失败分析 - lint (Job 944)
2025-05-29 20:47:35,536 - bot_agent.engines.simple_progress_tracker - WARNING - simple_progress_tracker.py:50 - send_progress_update - 缺少项目ID或Issue IID，跳过进度更新
2025-05-29 20:47:35,537 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-29 20:47:35,537 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://192.168.123.103/api/v4
2025-05-29 20:47:35,537 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://192.168.123.103/api/v4/version
2025-05-29 20:47:37,383 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:89 - _check_connection - Successfully connected to GitLab API: {'version': '17.11.1-ee', 'revision': 'cd4c186e8bc', 'kas': {'enabled': True, 'externalUrl': 'ws://192.168.123.103/-/kubernetes-agent/', 'externalK8sProxyUrl': 'http://192.168.123.103/-/kubernetes-agent/k8s-proxy/', 'version': '17.11.1'}, 'enterprise': True}
2025-05-29 20:47:37,383 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:99 - __init__ - 使用环境变量中的项目下载目录: E:\aider-git-repos\
2025-05-29 20:47:37,384 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\aider-git-repos\
2025-05-29 20:47:37,384 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-29 20:47:37,385 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:132 - setup_git_config - 使用环境变量中的Git用户名: aider-worker
2025-05-29 20:47:37,385 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:136 - setup_git_config - 使用环境变量中的Git用户邮箱: <EMAIL>
2025-05-29 20:47:37,386 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:143 - setup_git_config - 已设置Git环境变量: GIT_AUTHOR_NAME=aider-worker, GIT_COMMITTER_NAME=aider-worker
2025-05-29 20:47:37,386 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:148 - setup_git_config - 已设置Git环境变量: GIT_AUTHOR_EMAIL=<EMAIL>, GIT_COMMITTER_EMAIL=<EMAIL>
2025-05-29 20:47:37,388 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: information_query
2025-05-29 20:47:37,388 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: TerminalTools
2025-05-29 20:47:37,388 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: TestingTools
2025-05-29 20:47:37,388 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: LogAnalysisTools
2025-05-29 20:47:37,389 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: DatabaseTools
2025-05-29 20:47:37,389 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: DependencyTools
2025-05-29 20:47:37,389 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: DebugTools
2025-05-29 20:47:37,390 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: FrontendDebugTools
2025-05-29 20:47:37,390 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: RefactorTools
2025-05-29 20:47:37,391 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: DocumentationTools
2025-05-29 20:47:37,391 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: SecurityTools
2025-05-29 20:47:37,391 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: CodeGenerationTools
2025-05-29 20:47:37,392 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-29 20:47:37,392 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-29 20:47:37,393 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-29 20:47:37,393 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-29 20:47:37,393 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\aider-git-repos\
2025-05-29 20:47:37,394 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-29 20:47:37,394 - bot_agent.handlers.ai_processor - INFO - ai_processor.py:174 - _process_with_aider - 使用Aider执行引擎处理 TaskType.BUG_FIX 类型任务
2025-05-29 20:47:37,395 - bot_agent.engines.task_executor - INFO - task_executor.py:64 - execute_task - 开始使用Aider执行任务 b4a0a659-a273-4a12-82d2-3126a57375b4
2025-05-29 20:47:37,674 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 📍 注册监控点: prepare_workspace
2025-05-29 20:47:37,674 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目ID: 9
2025-05-29 20:47:37,675 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目信息: 9
2025-05-29 20:47:37,676 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:136 - _make_request - Making GET request to http://192.168.123.103/api/v4/projects/9
