#!/usr/bin/env python3
"""
测试智能日志分析 - 验证AI能够看到原始日志并正确识别错误类型
"""

import sys
import os
import asyncio
import traceback

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_smart_log_analysis():
    """测试智能日志分析"""
    print("🧠 智能日志分析测试")
    print("=" * 50)
    
    try:
        from bot_agent.tools.intelligent_tool_coordinator import global_tool_coordinator
        
        # 测试1：网络超时错误
        print("\n🌐 测试1：网络超时错误智能分析...")
        
        job_info = {
            'name': 'lint',
            'status': 'failed',
            'id': 927
        }
        
        # 模拟网络超时的作业日志
        network_timeout_log = """
[32;1m$ pip install black==23.3.0 flake8==6.0.0[0;m
Collecting black==23.3.0
  Downloading black-23.3.0-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.7 MB)
     âââââââââââââââââââââââââ¸                1.0/1.7 MB 36.0 kB/s eta 0:00:18
ERROR: Exception:
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/pip/_vendor/urllib3/response.py", line 438, in _error_catcher
    yield
  File "/usr/local/lib/python3.9/site-packages/pip/_vendor/urllib3/response.py", line 561, in read
    data = self._fp_read(amt) if not fp_closed else b""
  File "/usr/local/lib/python3.9/site-packages/pip/_vendor/urllib3/response.py", line 527, in _fp_read
    return self._fp.read(amt) if amt is not None else self._fp.read()
  File "/usr/local/lib/python3.9/site-packages/pip/_vendor/cachecontrol/filewrapper.py", line 90, in read
    data = self.__fp.read(amt)
  File "/usr/local/lib/python3.9/http/client.py", line 463, in read
    n = self.readinto(b)
  File "/usr/local/lib/python3.9/http/client.py", line 507, in readinto
    n = self.fp.readinto(b)
  File "/usr/local/lib/python3.9/socket.py", line 716, in readinto
    return self._sock.recv_into(b)
  File "/usr/local/lib/python3.9/ssl.py", line 1275, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/local/lib/python3.9/ssl.py", line 1133, in read
    return self._sslobj.read(len, buffer)
socket.timeout: The read operation timed out

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
pip._vendor.urllib3.exceptions.ReadTimeoutError: HTTPSConnectionPool(host='files.pythonhosted.org', port=443): Read timed out.
[0K[31;1mERROR: Job failed: exit code 1
"""
        
        project_path = "E:\\aider-git-repos\\ai-proxy"
        
        try:
            # 测试关键日志段落提取
            print("🔍 测试关键日志段落提取...")
            key_segments = global_tool_coordinator.async_coordinator._extract_key_log_segments(network_timeout_log)
            print(f"提取的关键段落:")
            print(key_segments)
            print()
            
            # 验证是否正确提取了网络超时相关的错误
            if 'socket.timeout' in key_segments and 'ReadTimeoutError' in key_segments:
                print("✅ 正确提取了网络超时错误信息")
                log_extraction_correct = True
            else:
                print("❌ 未能正确提取网络超时错误信息")
                log_extraction_correct = False
            
            # 测试AI修复方案生成（使用原始日志）
            print("\n🤖 测试AI修复方案生成（使用原始日志）...")
            
            # 模拟错误列表（这些是关键词匹配的结果，可能不准确）
            mock_errors = [
                {'type': 'build_error', 'content': 'ERROR: Job failed: exit code 1'}
            ]
            
            ai_fix_result = await global_tool_coordinator.async_coordinator._ai_generate_fix_plan(
                mock_errors, project_path, network_timeout_log, job_info
            )
            
            if ai_fix_result.success:
                print("✅ AI修复方案生成成功")
                
                fix_plan = []
                if ai_fix_result.data and isinstance(ai_fix_result.data, dict):
                    fix_plan = ai_fix_result.data.get('fix_plan', [])
                
                if fix_plan:
                    print(f"🤖 AI生成了 {len(fix_plan)} 个修复步骤")
                    
                    # 分析AI是否正确识别了网络问题
                    network_related_fixes = 0
                    config_related_fixes = 0
                    
                    for i, step in enumerate(fix_plan[:3]):  # 只分析前3个步骤
                        step_description = step.get('description', '') if isinstance(step, dict) else str(step)
                        step_command = step.get('command', '') if isinstance(step, dict) else ''
                        
                        print(f"   步骤{i+1}: {step_description}")
                        if step_command:
                            print(f"     命令: {step_command}")
                        
                        step_content = (step_description + ' ' + step_command).lower()
                        
                        if any(keyword in step_content for keyword in ['timeout', 'network', 'mirror', 'proxy', 'retry', 'pip']):
                            network_related_fixes += 1
                            print(f"     ✅ 正确识别为网络相关修复")
                        elif any(keyword in step_content for keyword in ['flake8', 'extend-ignore', '.flake8', 'config']):
                            config_related_fixes += 1
                            print(f"     ❌ 错误识别为配置相关修复")
                        else:
                            print(f"     ⚠️ 通用修复步骤")
                    
                    print(f"\n📊 修复方案分析:")
                    print(f"  - 网络相关修复: {network_related_fixes} 个")
                    print(f"  - 配置相关修复: {config_related_fixes} 个")
                    
                    if network_related_fixes > 0 and config_related_fixes == 0:
                        print("✅ AI正确识别了网络超时问题并生成了针对性修复方案")
                        ai_analysis_correct = True
                    elif config_related_fixes > 0:
                        print("❌ AI错误地将网络问题识别为配置问题")
                        ai_analysis_correct = False
                    else:
                        print("⚠️ AI生成了通用修复方案，未明确针对网络问题")
                        ai_analysis_correct = False
                else:
                    print("❌ AI修复方案为空")
                    ai_analysis_correct = False
            else:
                print("❌ AI修复方案生成失败")
                ai_analysis_correct = False
                
        except Exception as e:
            print(f"❌ 网络超时错误测试失败: {e}")
            print("🔍 详细错误堆栈:")
            traceback.print_exc()
            log_extraction_correct = False
            ai_analysis_correct = False
        
        # 测试2：flake8配置错误
        print("\n🔧 测试2：flake8配置错误智能分析...")
        
        # 模拟真正的flake8配置错误日志
        flake8_config_log = """
[32;1m$ flake8 --config .flake8[0;m
Traceback (most recent call last):
  File "/usr/local/bin/flake8", line 8, in <module>
    sys.exit(main())
  File "/usr/local/lib/python3.9/site-packages/flake8/main/cli.py", line 23, in main
    app.run(argv)
  File "/usr/local/lib/python3.9/site-packages/flake8/main/application.py", line 198, in run
    self._run(argv)
  File "/usr/local/lib/python3.9/site-packages/flake8/main/application.py", line 186, in _run
    self.initialize(argv)
  File "/usr/local/lib/python3.9/site-packages/flake8/main/application.py", line 165, in initialize
    self.plugins, self.options = parse_args(argv)
  File "/usr/local/lib/python3.9/site-packages/flake8/options/parse_args.py", line 53, in parse_args
    opts = aggregator.aggregate_options(option_manager, cfg, cfg_dir, rest)
  File "/usr/local/lib/python3.9/site-packages/flake8/options/aggregator.py", line 30, in aggregate_options
    parsed_config = config.parse_config(manager, cfg, cfg_dir)
  File "/usr/local/lib/python3.9/site-packages/flake8/options/config.py", line 131, in parse_config
    raise ValueError(
ValueError: Error code 'Ignore' supplied to 'extend-ignore' option does not match '^[A-Z]{1,3}[0-9]{0,3}$'
[0K[31;1mERROR: Job failed: exit code 1
"""
        
        try:
            # 测试关键日志段落提取
            print("🔍 测试flake8错误的关键日志段落提取...")
            key_segments = global_tool_coordinator.async_coordinator._extract_key_log_segments(flake8_config_log)
            print(f"提取的关键段落:")
            print(key_segments)
            print()
            
            # 验证是否正确提取了flake8配置错误
            if 'extend-ignore' in key_segments and 'ValueError' in key_segments:
                print("✅ 正确提取了flake8配置错误信息")
                flake8_extraction_correct = True
            else:
                print("❌ 未能正确提取flake8配置错误信息")
                flake8_extraction_correct = False
            
            # 测试AI修复方案生成（使用原始日志）
            print("\n🤖 测试AI修复方案生成（针对flake8配置错误）...")
            
            mock_errors = [
                {'type': 'build_error', 'content': 'ERROR: Job failed: exit code 1'}
            ]
            
            ai_fix_result = await global_tool_coordinator.async_coordinator._ai_generate_fix_plan(
                mock_errors, project_path, flake8_config_log, job_info
            )
            
            if ai_fix_result.success:
                print("✅ AI修复方案生成成功")
                
                fix_plan = []
                if ai_fix_result.data and isinstance(ai_fix_result.data, dict):
                    fix_plan = ai_fix_result.data.get('fix_plan', [])
                
                if fix_plan:
                    print(f"🤖 AI生成了 {len(fix_plan)} 个修复步骤")
                    
                    # 分析AI是否正确识别了flake8配置问题
                    config_related_fixes = 0
                    network_related_fixes = 0
                    
                    for i, step in enumerate(fix_plan[:3]):  # 只分析前3个步骤
                        step_description = step.get('description', '') if isinstance(step, dict) else str(step)
                        step_command = step.get('command', '') if isinstance(step, dict) else ''
                        
                        print(f"   步骤{i+1}: {step_description}")
                        if step_command:
                            print(f"     命令: {step_command}")
                        
                        step_content = (step_description + ' ' + step_command).lower()
                        
                        if any(keyword in step_content for keyword in ['flake8', 'extend-ignore', '.flake8', 'config']):
                            config_related_fixes += 1
                            print(f"     ✅ 正确识别为配置相关修复")
                        elif any(keyword in step_content for keyword in ['timeout', 'network', 'mirror', 'proxy']):
                            network_related_fixes += 1
                            print(f"     ❌ 错误识别为网络相关修复")
                        else:
                            print(f"     ⚠️ 通用修复步骤")
                    
                    print(f"\n📊 修复方案分析:")
                    print(f"  - 配置相关修复: {config_related_fixes} 个")
                    print(f"  - 网络相关修复: {network_related_fixes} 个")
                    
                    if config_related_fixes > 0 and network_related_fixes == 0:
                        print("✅ AI正确识别了flake8配置问题并生成了针对性修复方案")
                        flake8_analysis_correct = True
                    elif network_related_fixes > 0:
                        print("❌ AI错误地将配置问题识别为网络问题")
                        flake8_analysis_correct = False
                    else:
                        print("⚠️ AI生成了通用修复方案，未明确针对配置问题")
                        flake8_analysis_correct = False
                else:
                    print("❌ AI修复方案为空")
                    flake8_analysis_correct = False
            else:
                print("❌ AI修复方案生成失败")
                flake8_analysis_correct = False
                
        except Exception as e:
            print(f"❌ flake8配置错误测试失败: {e}")
            print("🔍 详细错误堆栈:")
            traceback.print_exc()
            flake8_extraction_correct = False
            flake8_analysis_correct = False
        
        # 总结测试结果
        print("\n🎯 智能日志分析测试结果总结:")
        print(f"  - 网络超时日志提取: {'✅ 正确' if log_extraction_correct else '❌ 错误'}")
        print(f"  - 网络超时AI分析: {'✅ 正确' if ai_analysis_correct else '❌ 错误'}")
        print(f"  - flake8配置日志提取: {'✅ 正确' if flake8_extraction_correct else '❌ 错误'}")
        print(f"  - flake8配置AI分析: {'✅ 正确' if flake8_analysis_correct else '❌ 错误'}")
        
        all_tests_passed = (log_extraction_correct and ai_analysis_correct and 
                           flake8_extraction_correct and flake8_analysis_correct)
        
        if all_tests_passed:
            print("\n🎉 所有测试通过！智能日志分析功能正常工作")
            print("\n💡 修复效果:")
            print("1. ✅ AI能够看到原始日志内容，不再依赖关键词匹配结果")
            print("2. ✅ AI能够正确区分网络超时错误和配置错误")
            print("3. ✅ AI能够生成针对性的修复方案")
            print("4. ✅ 关键日志段落提取功能正常工作，避免发送过长日志")
            print("\n🚀 现在task_1748505049_1748505049类似的任务应该能够正确处理！")
            return True
        else:
            print("\n❌ 部分测试失败！需要进一步调试")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print("🔍 详细错误堆栈:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_smart_log_analysis())
    sys.exit(0 if success else 1)
