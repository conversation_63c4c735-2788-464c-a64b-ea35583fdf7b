#!/usr/bin/env python3
"""
测试轮次顺序修复
验证轮次记录的顺序与执行顺序一致
"""

import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def test_execution_order():
    """测试执行顺序与记录顺序的一致性"""
    print("🔧 测试执行顺序与记录顺序的一致性")
    print("=" * 50)
    
    try:
        from bot_agent.utils.conversation_logger import global_conversation_logger
        from bot_agent.utils.conversation_logger import ConversationStatus
        
        # 创建测试会话
        session_id = global_conversation_logger.start_session(
            task_id="execution_order_test",
            task_title="执行顺序测试",
            task_type="job_failure_analysis",
            project_path="/test/path"
        )
        
        print(f"✅ 会话ID: {session_id}")
        
        # 模拟正确的执行顺序
        print("\n📋 按正确的执行顺序记录轮次:")
        
        # 1. 智能分析过程（应该是第1轮）
        print("  🔍 执行智能分析...")
        time.sleep(0.1)  # 模拟执行时间
        global_conversation_logger.log_round(
            session_id=session_id,
            round_name="智能作业失败分析",
            prompt="智能分析Job失败原因并执行修复",
            response="分析开始，检测到 3 个错误",
            model_name="intelligent-job-analyzer",
            duration=2.5
        )
        print("    ✅ 记录第1轮：智能作业失败分析")
        
        # 2. AI生成修复方案（应该是第2轮）
        print("  🤖 AI生成修复方案...")
        time.sleep(0.1)
        global_conversation_logger.log_round(
            session_id=session_id,
            round_name="AI生成修复方案",
            prompt="基于错误分析生成修复方案",
            response="AI修复方案生成成功: 生成了3个修复步骤",
            model_name="ai-fix-planner",
            duration=1.8
        )
        print("    ✅ 记录第2轮：AI生成修复方案")
        
        # 3. 修复步骤执行（应该是第3-5轮）
        for i in range(3):
            print(f"  🔧 执行修复步骤{i+1}...")
            time.sleep(0.1)
            global_conversation_logger.log_round(
                session_id=session_id,
                round_name=f"修复步骤{i+1} - flake8配置修复",
                prompt=f"执行修复步骤{i+1}",
                response=f"修复步骤{i+1}执行{'成功' if i < 2 else '失败'}",
                model_name="fix-executor",
                duration=1.2
            )
            print(f"    ✅ 记录第{i+3}轮：修复步骤{i+1}")
        
        # 4. 修复效果验证（应该是第6轮）
        print("  ✅ 验证修复效果...")
        time.sleep(0.1)
        global_conversation_logger.log_round(
            session_id=session_id,
            round_name="修复效果验证",
            prompt="验证修复效果",
            response="验证失败，仍有1个错误未修复",
            model_name="fix-verifier",
            duration=1.5
        )
        print("    ✅ 记录第6轮：修复效果验证")
        
        # 5. 第二轮智能修复（应该是第7轮）
        print("  🔄 执行第二轮智能修复...")
        time.sleep(0.1)
        global_conversation_logger.log_round(
            session_id=session_id,
            round_name="第二轮智能修复",
            prompt="基于第一轮修复结果进行智能修复",
            response="第二轮修复成功",
            model_name="second-round-fixer",
            duration=2.1
        )
        print("    ✅ 记录第7轮：第二轮智能修复")
        
        # 结束会话
        global_conversation_logger.end_session(session_id, "测试完成", ConversationStatus.SUCCESS)
        
        # 验证结果
        print(f"\n📊 验证执行顺序...")
        session = global_conversation_logger._load_session_from_file(session_id)
        
        if session and session.rounds:
            print(f"  - 总轮次数: {len(session.rounds)}")
            
            # 期望的轮次顺序
            expected_order = [
                "智能作业失败分析",
                "AI生成修复方案", 
                "修复步骤1 - flake8配置修复",
                "修复步骤2 - flake8配置修复",
                "修复步骤3 - flake8配置修复",
                "修复效果验证",
                "第二轮智能修复"
            ]
            
            all_correct = True
            for i, (round_info, expected_name) in enumerate(zip(session.rounds, expected_order), 1):
                actual_round_number = round_info.round_number
                actual_name = round_info.round_name
                
                # 检查轮次编号是否连续
                if actual_round_number == i:
                    print(f"  ✅ 轮次{actual_round_number}: {actual_name}")
                else:
                    print(f"  ❌ 轮次编号错误: 期望{i}, 实际{actual_round_number}: {actual_name}")
                    all_correct = False
                
                # 检查轮次名称是否符合预期
                if expected_name in actual_name or actual_name.endswith(expected_name):
                    pass  # 名称正确
                else:
                    print(f"    ⚠️  轮次名称不符合预期: 期望包含'{expected_name}', 实际'{actual_name}'")
            
            # 检查时间戳顺序
            timestamps = [round_info.timestamp for round_info in session.rounds]
            if timestamps == sorted(timestamps):
                print(f"  ✅ 时间戳顺序正确")
            else:
                print(f"  ❌ 时间戳顺序混乱")
                all_correct = False
            
            if all_correct:
                print(f"\n🎉 执行顺序测试通过！")
                print(f"- 轮次编号连续且正确")
                print(f"- 轮次记录顺序与执行顺序一致")
                print(f"- 时间戳顺序正确")
                return True
            else:
                print(f"\n❌ 执行顺序测试失败")
                return False
        else:
            print(f"❌ 无法加载测试会话")
            return False
            
    except Exception as e:
        print(f"❌ 执行顺序测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_async_execution_simulation():
    """测试异步执行情况下的轮次记录"""
    print("\n🔧 测试异步执行情况下的轮次记录")
    print("=" * 50)
    
    try:
        from bot_agent.utils.conversation_logger import global_conversation_logger
        from bot_agent.utils.conversation_logger import ConversationStatus
        import threading
        import random
        
        # 创建测试会话
        session_id = global_conversation_logger.start_session(
            task_id="async_execution_test",
            task_title="异步执行测试",
            task_type="job_failure_analysis",
            project_path="/test/path"
        )
        
        print(f"✅ 会话ID: {session_id}")
        
        # 模拟异步执行（但记录顺序应该保持正确）
        print("\n📋 模拟异步执行但按顺序记录:")
        
        def record_round(round_num, name, delay):
            """模拟异步执行的轮次记录"""
            time.sleep(delay)  # 模拟不同的执行时间
            global_conversation_logger.log_round(
                session_id=session_id,
                round_name=name,
                prompt=f"异步执行{round_num}",
                response=f"异步执行{round_num}完成",
                model_name=f"async-executor-{round_num}",
                duration=delay
            )
            print(f"    ✅ 记录完成：{name} (延迟{delay:.1f}s)")
        
        # 按顺序启动异步任务（但执行时间不同）
        rounds_info = [
            ("智能分析", 0.1),
            ("修复方案生成", 0.3),
            ("修复执行", 0.2),
            ("效果验证", 0.1),
            ("二次修复", 0.2)
        ]
        
        # 顺序记录（即使执行时间不同）
        for i, (name, delay) in enumerate(rounds_info, 1):
            print(f"  🚀 启动第{i}轮：{name}")
            record_round(i, name, delay)
        
        # 结束会话
        global_conversation_logger.end_session(session_id, "异步测试完成", ConversationStatus.SUCCESS)
        
        # 验证结果
        print(f"\n📊 验证异步执行结果...")
        session = global_conversation_logger._load_session_from_file(session_id)
        
        if session and session.rounds:
            print(f"  - 总轮次数: {len(session.rounds)}")
            
            all_correct = True
            for i, round_info in enumerate(session.rounds, 1):
                if round_info.round_number == i:
                    print(f"  ✅ 轮次{i}: {round_info.round_name}")
                else:
                    print(f"  ❌ 轮次编号错误: 期望{i}, 实际{round_info.round_number}")
                    all_correct = False
            
            if all_correct:
                print(f"\n🎉 异步执行测试通过！")
                print(f"- 即使异步执行，轮次编号仍然保持正确顺序")
                return True
            else:
                print(f"\n❌ 异步执行测试失败")
                return False
        else:
            print(f"❌ 无法加载测试会话")
            return False
            
    except Exception as e:
        print(f"❌ 异步执行测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始轮次顺序修复验证")
    
    results = []
    
    # 测试1: 执行顺序一致性
    results.append(test_execution_order())
    
    # 测试2: 异步执行情况
    results.append(test_async_execution_simulation())
    
    print(f"\n📊 测试结果汇总:")
    print(f"  - 总测试数: {len(results)}")
    print(f"  - 通过数: {sum(results)}")
    print(f"  - 失败数: {len(results) - sum(results)}")
    print(f"  - 成功率: {sum(results) / len(results) * 100:.1f}%")
    
    if all(results):
        print(f"\n🎉 所有测试通过！轮次顺序问题已修复！")
        print(f"\n🔧 修复要点:")
        print(f"1. 将智能分析记录移到了执行流程的开始")
        print(f"2. 确保轮次记录顺序与实际执行顺序一致")
        print(f"3. 移除了重复的轮次记录")
        print(f"4. 使用自动轮次编号确保连续性")
        print(f"5. 在最后更新第1轮的完整分析结果")
    else:
        print(f"\n❌ 部分测试失败，需要进一步修复")

if __name__ == "__main__":
    main()
