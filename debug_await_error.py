#!/usr/bin/env python3
"""
调试 'object ToolResult can't be used in 'await' expression' 错误
"""

import sys
import os
import asyncio
import traceback

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def debug_await_error():
    """调试异步调用错误"""
    print("🔍 调试 'object ToolResult can't be used in 'await' expression' 错误")

    try:
        from bot_agent.tools.intelligent_tool_coordinator import global_tool_coordinator

        # 模拟错误列表
        test_errors = [
            "ValueError: Error code 'Ignore' supplied to 'extend-ignore' option does not match '^[A-Z]{1,3}[0-9]{0,3}$'"
        ]

        project_path = "E:\\aider-git-repos\\ai-proxy"

        print(f"📋 测试参数:")
        print(f"  - 错误数量: {len(test_errors)}")
        print(f"  - 项目路径: {project_path}")

        # 测试AI生成修复方案
        print("\n🤖 测试AI生成修复方案...")
        try:
            # 正确的调用方式：使用async_coordinator
            ai_fix_result = await global_tool_coordinator.async_coordinator._ai_generate_fix_plan(test_errors, project_path)
            print(f"✅ AI修复方案生成: {'成功' if ai_fix_result.success else '失败'}")
            print(f"   消息: {ai_fix_result.message}")

            if ai_fix_result.data:
                fix_plan = ai_fix_result.data.get('fix_plan', [])
                print(f"   修复步骤数: {len(fix_plan)}")

        except Exception as e:
            print(f"❌ AI修复方案生成失败: {e}")
            print("🔍 详细错误堆栈:")
            traceback.print_exc()
            return False

        return True

    except Exception as e:
        print(f"❌ 调试失败: {e}")
        print("🔍 详细错误堆栈:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(debug_await_error())
    sys.exit(0 if success else 1)
