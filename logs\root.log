2025-05-28 19:17:24,259 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 19:17:24,260 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 19:17:24,260 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 19:17:24,262 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 19:17:24,263 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 19:17:24,485 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 19:17:24,485 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 19:17:24,487 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:71 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 19:17:24,589 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 19:17:24,589 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 19:17:24,840 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 19:17:24,840 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 19:17:24,841 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:71 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 19:17:24,845 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:873 - _fix_dependency_errors - 🔧 修复依赖错误: 1 个
2025-05-28 19:17:24,847 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1068 - _fix_requirements_txt - 修复requirements.txt第9行: [dev]
2025-05-28 19:17:24,847 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1077 - _fix_requirements_txt - 修复无效依赖声明: [dev] -> # [dev]  # 无效的依赖声明，已注释
2025-05-28 19:17:24,848 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1093 - _fix_requirements_txt - ✅ 成功修复requirements.txt第9行
2025-05-28 19:17:24,853 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 19:17:24,853 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 19:17:26,895 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 19:17:26,896 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 19:17:26,896 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:71 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 19:21:47,972 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 19:21:47,972 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 19:21:47,972 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 19:21:47,973 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 19:21:47,974 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 19:21:48,198 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 19:21:48,198 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 19:21:48,200 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 19:21:48,301 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 19:21:48,302 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 19:21:48,410 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 19:21:48,411 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 19:21:48,411 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 19:21:48,417 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:188 - execute_targeted_fixes - 🤖 开始AI驱动的智能修复...
2025-05-28 19:21:48,417 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:256 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-28 19:21:48,420 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:73 - __init__ - LLM重试处理器初始化完成
2025-05-28 19:21:48,610 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 1): OPENROUTER_API_KEY环境变量未设置
2025-05-28 19:21:48,611 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 1.2 秒后重试...
2025-05-28 19:21:49,772 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 2): OPENROUTER_API_KEY环境变量未设置
2025-05-28 19:21:49,772 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 2.3 秒后重试...
2025-05-28 19:21:52,117 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 3): OPENROUTER_API_KEY环境变量未设置
2025-05-28 19:21:52,117 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 4.9 秒后重试...
2025-05-28 19:21:57,032 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:160 - execute_with_retry - 达到最大重试次数，使用降级策略
2025-05-28 19:21:57,032 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:376 - _ai_generate_fix_plan - AI修复方案生成异常: 'str' object has no attribute 'get'
2025-05-28 19:21:57,032 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:237 - execute_targeted_fixes - AI修复方案生成失败，回退到策略修复
2025-05-28 19:21:57,033 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:485 - _fallback_strategy_fixes - 🔄 回退到策略修复模式...
2025-05-28 19:21:57,033 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:491 - _fallback_strategy_fixes - 🔧 处理 other_errors 类型错误: 1 个
2025-05-28 19:21:57,033 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:507 - _fallback_strategy_fixes - 修复结果: {'success': True, 'message': '尝试修复 1 个通用错误'}
2025-05-28 19:21:57,033 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:491 - _fallback_strategy_fixes - 🔧 处理 build_errors 类型错误: 1 个
2025-05-28 19:21:57,034 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:997 - _fix_build_errors - 🔧 修复构建错误: 1 个
2025-05-28 19:21:57,034 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:507 - _fallback_strategy_fixes - 修复结果: {'success': False, 'message': '修复了 0/1 个构建错误', 'details': [], 'fixed_count': 0}
2025-05-28 20:05:44,425 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 20:05:44,427 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 20:05:44,427 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 20:05:44,428 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 20:05:44,428 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 20:05:45,247 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 20:05:45,247 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 20:05:45,249 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 20:05:45,350 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 20:05:45,350 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 20:05:45,451 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 20:05:45,451 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 20:05:45,452 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 20:05:45,452 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 20:05:45,452 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 20:05:45,557 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 20:05:45,557 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 20:05:45,558 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 20:05:45,659 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 20:05:45,659 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 20:05:45,813 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 20:05:45,814 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 20:05:45,814 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 20:33:55,265 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 20:33:55,266 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 20:33:55,267 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 20:33:55,268 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 20:33:55,268 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 20:33:55,441 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 20:33:55,442 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 20:33:55,442 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 20:33:55,545 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 20:33:55,545 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 20:33:55,720 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 20:33:55,721 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 20:33:55,721 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 20:33:55,727 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:468 - _execute_command_with_retry - 🔄 第 1 次尝试执行: grep -r 'test' ./*
2025-05-28 20:33:59,226 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "grep -r 'test' ./*"
2025-05-28 20:33:59,226 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: grep : 无法将“grep”项识别为 cmdlet、函数、脚本文件或可运行程序的名称。请检查名称的拼写，如果包括路径，请确保
路径正确，然后再试一次。
所在位置 行:1 字符: 1
+ grep -r 'test' ./*
+ ~~~~
    + CategoryInfo          : ObjectNotFound: (grep:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 

2025-05-28 20:33:59,227 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:496 - _execute_command_with_retry - ❌ 第 1 次尝试失败: grep : 无法将“grep”项识别为 cmdlet、函数、脚本文件或可运行程序的名称。请检查名称的拼写，如果包括路径，请确保
路径正确，然后再试一次。
所在位置 行:1 字符: 1
+ grep -r 'test' ./*
+ ~~~~
    + CategoryInfo          : ObjectNotFound: (grep:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 

2025-05-28 20:33:59,227 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:535 - _get_ai_alternative_command - 🤖 请求AI生成替代命令...
2025-05-28 20:33:59,230 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:73 - __init__ - LLM重试处理器初始化完成
2025-05-28 20:34:08,154 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:650 - _get_ai_alternative_command - AI替代命令生成失败: 未知错误
2025-05-28 20:34:08,154 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:511 - _execute_command_with_retry - ⚠️ AI无法提供替代命令，继续使用原命令
2025-05-28 20:34:08,154 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:468 - _execute_command_with_retry - 🔄 第 2 次尝试执行: grep -r 'test' ./*
2025-05-28 20:34:11,485 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "grep -r 'test' ./*"
2025-05-28 20:34:11,486 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: grep : 无法将“grep”项识别为 cmdlet、函数、脚本文件或可运行程序的名称。请检查名称的拼写，如果包括路径，请确保
路径正确，然后再试一次。
所在位置 行:1 字符: 1
+ grep -r 'test' ./*
+ ~~~~
    + CategoryInfo          : ObjectNotFound: (grep:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 

2025-05-28 20:34:11,486 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:496 - _execute_command_with_retry - ❌ 第 2 次尝试失败: grep : 无法将“grep”项识别为 cmdlet、函数、脚本文件或可运行程序的名称。请检查名称的拼写，如果包括路径，请确保
路径正确，然后再试一次。
所在位置 行:1 字符: 1
+ grep -r 'test' ./*
+ ~~~~
    + CategoryInfo          : ObjectNotFound: (grep:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 

2025-05-28 20:34:11,488 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 20:34:11,488 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 20:34:11,689 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 20:34:11,689 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 20:34:11,690 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 20:34:11,691 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:535 - _get_ai_alternative_command - 🤖 请求AI生成替代命令...
2025-05-28 20:34:20,532 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:650 - _get_ai_alternative_command - AI替代命令生成失败: 未知错误
2025-05-28 20:34:20,535 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:535 - _get_ai_alternative_command - 🤖 请求AI生成替代命令...
2025-05-28 20:34:30,076 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:650 - _get_ai_alternative_command - AI替代命令生成失败: 未知错误
2025-05-28 20:34:30,080 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:535 - _get_ai_alternative_command - 🤖 请求AI生成替代命令...
2025-05-28 20:34:38,898 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:650 - _get_ai_alternative_command - AI替代命令生成失败: 未知错误
2025-05-28 20:34:38,900 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 20:34:38,900 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 20:34:39,066 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 20:34:39,066 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 20:34:39,067 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 20:34:39,068 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:440 - _execute_ai_fix_step - 🔧 执行AI修复步骤: 搜索项目中的配置错误
2025-05-28 20:34:39,069 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:441 - _execute_ai_fix_step - 📝 执行命令: grep -r "extend-ignore.*#" ./*
2025-05-28 20:34:39,069 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:468 - _execute_command_with_retry - 🔄 第 1 次尝试执行: grep -r "extend-ignore.*#" ./*
2025-05-28 20:34:42,441 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "grep -r "extend-ignore.*#" ./*"
2025-05-28 20:34:42,442 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: grep : 无法将“grep”项识别为 cmdlet、函数、脚本文件或可运行程序的名称。请检查名称的拼写，如果包括路径，请确保
路径正确，然后再试一次。
所在位置 行:1 字符: 1
+ grep -r extend-ignore.*# ./*
+ ~~~~
    + CategoryInfo          : ObjectNotFound: (grep:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 

2025-05-28 20:34:42,442 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:496 - _execute_command_with_retry - ❌ 第 1 次尝试失败: grep : 无法将“grep”项识别为 cmdlet、函数、脚本文件或可运行程序的名称。请检查名称的拼写，如果包括路径，请确保
路径正确，然后再试一次。
所在位置 行:1 字符: 1
+ grep -r extend-ignore.*# ./*
+ ~~~~
    + CategoryInfo          : ObjectNotFound: (grep:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 

2025-05-28 20:34:42,442 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:535 - _get_ai_alternative_command - 🤖 请求AI生成替代命令...
2025-05-28 20:34:51,892 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:650 - _get_ai_alternative_command - AI替代命令生成失败: 未知错误
2025-05-28 20:34:51,893 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:511 - _execute_command_with_retry - ⚠️ AI无法提供替代命令，继续使用原命令
2025-05-28 20:34:51,894 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:468 - _execute_command_with_retry - 🔄 第 2 次尝试执行: grep -r "extend-ignore.*#" ./*
2025-05-28 20:34:55,225 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "grep -r "extend-ignore.*#" ./*"
2025-05-28 20:34:55,225 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: grep : 无法将“grep”项识别为 cmdlet、函数、脚本文件或可运行程序的名称。请检查名称的拼写，如果包括路径，请确保
路径正确，然后再试一次。
所在位置 行:1 字符: 1
+ grep -r extend-ignore.*# ./*
+ ~~~~
    + CategoryInfo          : ObjectNotFound: (grep:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 

2025-05-28 20:34:55,226 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:496 - _execute_command_with_retry - ❌ 第 2 次尝试失败: grep : 无法将“grep”项识别为 cmdlet、函数、脚本文件或可运行程序的名称。请检查名称的拼写，如果包括路径，请确保
路径正确，然后再试一次。
所在位置 行:1 字符: 1
+ grep -r extend-ignore.*# ./*
+ ~~~~
    + CategoryInfo          : ObjectNotFound: (grep:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 

2025-05-28 20:34:55,226 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:535 - _get_ai_alternative_command - 🤖 请求AI生成替代命令...
2025-05-28 20:35:04,140 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:650 - _get_ai_alternative_command - AI替代命令生成失败: 未知错误
2025-05-28 20:35:04,141 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:511 - _execute_command_with_retry - ⚠️ AI无法提供替代命令，继续使用原命令
2025-05-28 20:35:04,141 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:468 - _execute_command_with_retry - 🔄 第 3 次尝试执行: grep -r "extend-ignore.*#" ./*
2025-05-28 20:35:07,457 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "grep -r "extend-ignore.*#" ./*"
2025-05-28 20:35:07,458 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: grep : 无法将“grep”项识别为 cmdlet、函数、脚本文件或可运行程序的名称。请检查名称的拼写，如果包括路径，请确保
路径正确，然后再试一次。
所在位置 行:1 字符: 1
+ grep -r extend-ignore.*# ./*
+ ~~~~
    + CategoryInfo          : ObjectNotFound: (grep:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 

2025-05-28 20:35:07,458 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:496 - _execute_command_with_retry - ❌ 第 3 次尝试失败: grep : 无法将“grep”项识别为 cmdlet、函数、脚本文件或可运行程序的名称。请检查名称的拼写，如果包括路径，请确保
路径正确，然后再试一次。
所在位置 行:1 字符: 1
+ grep -r extend-ignore.*# ./*
+ ~~~~
    + CategoryInfo          : ObjectNotFound: (grep:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 

2025-05-28 20:37:19,678 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 20:37:19,679 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 20:37:19,679 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 20:37:19,680 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 20:37:19,681 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 20:37:20,001 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 20:37:20,002 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 20:37:20,003 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 20:37:20,104 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 20:37:20,104 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 20:37:20,297 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 20:37:20,297 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 20:37:20,298 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 20:37:20,303 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:468 - _execute_command_with_retry - 🔄 第 1 次尝试执行: grep -r 'test' ./*
2025-05-28 20:37:24,027 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "grep -r 'test' ./*"
2025-05-28 20:37:24,027 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: grep : 无法将“grep”项识别为 cmdlet、函数、脚本文件或可运行程序的名称。请检查名称的拼写，如果包括路径，请确保
路径正确，然后再试一次。
所在位置 行:1 字符: 1
+ grep -r 'test' ./*
+ ~~~~
    + CategoryInfo          : ObjectNotFound: (grep:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 

2025-05-28 20:37:24,027 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:496 - _execute_command_with_retry - ❌ 第 1 次尝试失败: grep : 无法将“grep”项识别为 cmdlet、函数、脚本文件或可运行程序的名称。请检查名称的拼写，如果包括路径，请确保
路径正确，然后再试一次。
所在位置 行:1 字符: 1
+ grep -r 'test' ./*
+ ~~~~
    + CategoryInfo          : ObjectNotFound: (grep:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 

2025-05-28 20:37:24,028 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:535 - _get_ai_alternative_command - 🔄 生成替代命令...
2025-05-28 20:37:24,028 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:540 - _get_ai_alternative_command - 📋 使用预定义替代命令: Select-String -Pattern "test" -Path ./* -Recurse
2025-05-28 20:37:24,029 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:508 - _execute_command_with_retry - 🤖 AI建议替代命令: Select-String -Pattern "test" -Path ./* -Recurse
2025-05-28 20:37:24,029 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:468 - _execute_command_with_retry - 🔄 第 2 次尝试执行: Select-String -Pattern "test" -Path ./* -Recurse
2025-05-28 20:37:27,234 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "Select-String -Pattern "test" -Path ./* -Recurse"
2025-05-28 20:37:27,234 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: Select-String : 找不到与参数名称“Recurse”匹配的参数。
所在位置 行:1 字符: 39
+ Select-String -Pattern test -Path ./* -Recurse
+                                       ~~~~~~~~
    + CategoryInfo          : InvalidArgument: (:) [Select-String]，ParameterBindingException
    + FullyQualifiedErrorId : NamedParameterNotFound,Microsoft.PowerShell.Commands.SelectStringCommand
 

2025-05-28 20:37:27,235 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:496 - _execute_command_with_retry - ❌ 第 2 次尝试失败: Select-String : 找不到与参数名称“Recurse”匹配的参数。
所在位置 行:1 字符: 39
+ Select-String -Pattern test -Path ./* -Recurse
+                                       ~~~~~~~~
    + CategoryInfo          : InvalidArgument: (:) [Select-String]，ParameterBindingException
    + FullyQualifiedErrorId : NamedParameterNotFound,Microsoft.PowerShell.Commands.SelectStringCommand
 

2025-05-28 20:37:27,238 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 20:37:27,238 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 20:37:28,332 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 20:37:28,333 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 20:37:28,333 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 20:37:28,335 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:535 - _get_ai_alternative_command - 🔄 生成替代命令...
2025-05-28 20:37:28,335 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:540 - _get_ai_alternative_command - 📋 使用预定义替代命令: Select-String -Pattern "test" -Path ./* -Recurse
2025-05-28 20:37:28,337 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:535 - _get_ai_alternative_command - 🔄 生成替代命令...
2025-05-28 20:37:28,338 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:540 - _get_ai_alternative_command - 📋 使用预定义替代命令: Get-ChildItem -Path . -Name "*.py" -Recurse
2025-05-28 20:37:28,340 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:535 - _get_ai_alternative_command - 🔄 生成替代命令...
2025-05-28 20:37:28,340 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:540 - _get_ai_alternative_command - 📋 使用预定义替代命令: Get-Content "requirements.txt"
2025-05-28 20:37:28,341 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 20:37:28,341 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 20:37:30,572 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 20:37:30,572 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 20:37:30,573 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 20:37:30,574 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:440 - _execute_ai_fix_step - 🔧 执行AI修复步骤: 搜索项目中的配置错误
2025-05-28 20:37:30,575 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:441 - _execute_ai_fix_step - 📝 执行命令: grep -r "extend-ignore.*#" ./*
2025-05-28 20:37:30,575 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:468 - _execute_command_with_retry - 🔄 第 1 次尝试执行: grep -r "extend-ignore.*#" ./*
2025-05-28 20:37:33,896 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "grep -r "extend-ignore.*#" ./*"
2025-05-28 20:37:33,897 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: grep : 无法将“grep”项识别为 cmdlet、函数、脚本文件或可运行程序的名称。请检查名称的拼写，如果包括路径，请确保
路径正确，然后再试一次。
所在位置 行:1 字符: 1
+ grep -r extend-ignore.*# ./*
+ ~~~~
    + CategoryInfo          : ObjectNotFound: (grep:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 

2025-05-28 20:37:33,897 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:496 - _execute_command_with_retry - ❌ 第 1 次尝试失败: grep : 无法将“grep”项识别为 cmdlet、函数、脚本文件或可运行程序的名称。请检查名称的拼写，如果包括路径，请确保
路径正确，然后再试一次。
所在位置 行:1 字符: 1
+ grep -r extend-ignore.*# ./*
+ ~~~~
    + CategoryInfo          : ObjectNotFound: (grep:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 

2025-05-28 20:37:33,898 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:535 - _get_ai_alternative_command - 🔄 生成替代命令...
2025-05-28 20:37:33,898 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:540 - _get_ai_alternative_command - 📋 使用预定义替代命令: Select-String -Pattern "extend-ignore.*#" -Path ./* -Recurse
2025-05-28 20:37:33,898 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:508 - _execute_command_with_retry - 🤖 AI建议替代命令: Select-String -Pattern "extend-ignore.*#" -Path ./* -Recurse
2025-05-28 20:37:33,898 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:468 - _execute_command_with_retry - 🔄 第 2 次尝试执行: Select-String -Pattern "extend-ignore.*#" -Path ./* -Recurse
2025-05-28 20:37:37,035 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "Select-String -Pattern "extend-ignore.*#" -Path ./* -Recurse"
2025-05-28 20:37:37,035 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: Select-String : 找不到与参数名称“Recurse”匹配的参数。
所在位置 行:1 字符: 51
+ Select-String -Pattern extend-ignore.*# -Path ./* -Recurse
+                                                   ~~~~~~~~
    + CategoryInfo          : InvalidArgument: (:) [Select-String]，ParameterBindingException
    + FullyQualifiedErrorId : NamedParameterNotFound,Microsoft.PowerShell.Commands.SelectStringCommand
 

2025-05-28 20:37:37,035 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:496 - _execute_command_with_retry - ❌ 第 2 次尝试失败: Select-String : 找不到与参数名称“Recurse”匹配的参数。
所在位置 行:1 字符: 51
+ Select-String -Pattern extend-ignore.*# -Path ./* -Recurse
+                                                   ~~~~~~~~
    + CategoryInfo          : InvalidArgument: (:) [Select-String]，ParameterBindingException
    + FullyQualifiedErrorId : NamedParameterNotFound,Microsoft.PowerShell.Commands.SelectStringCommand
 

2025-05-28 20:37:37,036 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:535 - _get_ai_alternative_command - 🔄 生成替代命令...
2025-05-28 20:37:37,036 - bot_agent.tools.intelligent_tool_coordinator - DEBUG - intelligent_tool_coordinator.py:617 - _try_ai_alternative - OPENROUTER_API_KEY未设置，跳过AI生成
2025-05-28 20:37:37,036 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:549 - _get_ai_alternative_command - ⚠️ 无法生成替代命令
2025-05-28 20:37:37,036 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:511 - _execute_command_with_retry - ⚠️ AI无法提供替代命令，继续使用原命令
2025-05-28 20:37:37,037 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:468 - _execute_command_with_retry - 🔄 第 3 次尝试执行: Select-String -Pattern "extend-ignore.*#" -Path ./* -Recurse
2025-05-28 20:37:40,201 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "Select-String -Pattern "extend-ignore.*#" -Path ./* -Recurse"
2025-05-28 20:37:40,201 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: Select-String : 找不到与参数名称“Recurse”匹配的参数。
所在位置 行:1 字符: 51
+ Select-String -Pattern extend-ignore.*# -Path ./* -Recurse
+                                                   ~~~~~~~~
    + CategoryInfo          : InvalidArgument: (:) [Select-String]，ParameterBindingException
    + FullyQualifiedErrorId : NamedParameterNotFound,Microsoft.PowerShell.Commands.SelectStringCommand
 

2025-05-28 20:37:40,202 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:496 - _execute_command_with_retry - ❌ 第 3 次尝试失败: Select-String : 找不到与参数名称“Recurse”匹配的参数。
所在位置 行:1 字符: 51
+ Select-String -Pattern extend-ignore.*# -Path ./* -Recurse
+                                                   ~~~~~~~~
    + CategoryInfo          : InvalidArgument: (:) [Select-String]，ParameterBindingException
    + FullyQualifiedErrorId : NamedParameterNotFound,Microsoft.PowerShell.Commands.SelectStringCommand
 

2025-05-28 20:39:32,117 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 20:39:32,118 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 20:39:32,118 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 20:39:32,119 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 20:39:32,120 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 20:39:32,298 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 20:39:32,298 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 20:39:32,299 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 20:39:32,401 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 20:39:32,401 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 20:39:32,605 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 20:39:32,605 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 20:39:32,607 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 20:54:27,914 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 20:54:27,915 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 20:54:27,915 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 20:54:27,916 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-28 20:54:27,916 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-28 20:54:27,918 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 20:54:27,918 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 20:54:28,830 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 20:54:28,831 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 20:54:28,831 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-28 20:54:28,834 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 20:54:28,834 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 20:54:29,073 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 20:54:29,074 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 20:54:29,075 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 20:54:29,177 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-28 20:54:29,178 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 20:54:29,178 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 20:54:29,372 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 20:54:29,372 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 20:54:29,373 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-28 20:54:29,374 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-28 20:54:29,374 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-28 20:54:29,375 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-28 20:54:29,375 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-28 20:54:29,375 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-28 20:54:29,376 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-28 20:54:29,376 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-28 20:54:29,376 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-28 20:54:29,376 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-28 20:54:29,376 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-28 20:54:29,377 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-28 20:54:29,377 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-28 20:54:29,377 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-28 20:54:29,377 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-28 20:54:29,377 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-28 20:54:29,378 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-28 20:54:29,378 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-28 20:54:29,379 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-28 20:54:29,379 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-28 20:54:29,380 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-28 20:54:29,380 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 20:54:29,380 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 20:54:30,012 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 20:54:30,012 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 20:54:30,012 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 20:54:30,015 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 20:54:30,015 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 20:54:30,528 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 20:54:30,529 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 20:54:30,529 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-28 20:54:30,530 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-28 20:54:30,530 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-28 20:54:30,531 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-28 20:54:30,531 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-28 20:54:30,531 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-28 20:54:30,531 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-28 20:54:30,531 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-28 20:54:30,531 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-28 20:54:30,531 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-28 20:54:30,532 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-28 20:54:30,532 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-28 20:54:30,532 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-28 20:54:30,532 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-28 20:54:30,532 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-28 20:54:30,533 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-28 20:54:30,533 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-28 20:54:30,534 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-28 20:54:30,534 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-28 20:54:30,534 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-28 20:54:30,534 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-28 20:54:30,536 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 20:54:30,536 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 20:54:30,839 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 20:54:30,840 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 20:54:30,840 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-28 20:54:30,841 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-28 20:54:30,841 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-28 20:54:30,841 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-28 20:54:30,842 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-28 20:54:30,842 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-28 20:54:30,842 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-28 20:54:30,842 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-28 20:54:30,842 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-28 20:54:30,842 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-28 20:54:30,843 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-28 20:54:30,843 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-28 20:54:30,843 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-28 20:54:30,843 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-28 20:54:30,844 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-28 20:54:30,844 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-28 20:54:30,845 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-28 20:54:30,845 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-28 20:54:30,845 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-28 20:54:30,845 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-28 20:54:30,845 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-28 20:59:36,950 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 20:59:36,951 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 20:59:36,951 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 20:59:36,951 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-28 20:59:36,952 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-28 20:59:36,953 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 20:59:36,953 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 20:59:39,465 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 20:59:39,466 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 20:59:39,466 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-28 20:59:39,466 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-28 20:59:39,467 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 20:59:39,467 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 20:59:39,661 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 20:59:39,662 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 20:59:39,662 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-28 20:59:39,662 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-28 20:59:39,663 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-28 20:59:39,664 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-28 20:59:39,665 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-28 20:59:39,665 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-28 20:59:39,665 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-28 20:59:39,665 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-28 20:59:39,665 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-28 20:59:39,666 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-28 20:59:39,666 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-28 20:59:39,666 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-28 20:59:39,666 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-28 20:59:39,666 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-28 20:59:39,667 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-28 20:59:39,667 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-28 20:59:39,668 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-28 20:59:39,668 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-28 20:59:39,668 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-28 20:59:39,668 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-28 20:59:39,669 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-28 21:02:00,324 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 21:02:00,325 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 21:02:00,325 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 21:02:00,326 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-28 21:02:00,326 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-28 21:02:00,327 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 21:02:00,328 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 21:02:00,474 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 21:02:00,474 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 21:02:00,475 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-28 21:02:00,475 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-28 21:02:00,476 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 21:02:00,476 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 21:02:00,628 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 21:02:00,629 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 21:02:00,629 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-28 21:02:00,630 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-28 21:02:00,630 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-28 21:02:00,631 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-28 21:02:00,632 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-28 21:02:00,632 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-28 21:02:00,632 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-28 21:02:00,632 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-28 21:02:00,633 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-28 21:02:00,633 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-28 21:02:00,633 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-28 21:02:00,633 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-28 21:02:00,633 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-28 21:02:00,634 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-28 21:02:00,634 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-28 21:02:00,634 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-28 21:02:00,634 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-28 21:02:00,635 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-28 21:02:00,635 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-28 21:02:00,635 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-28 21:02:00,636 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-28 21:02:32,270 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 21:02:32,270 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 21:02:32,270 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 21:02:32,272 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-28 21:02:32,272 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-28 21:02:32,273 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 21:02:32,273 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 21:02:32,381 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 21:02:32,381 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 21:02:32,382 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-28 21:02:32,386 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 21:02:32,386 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 21:02:32,521 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 21:02:32,521 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 21:02:32,522 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 21:02:32,625 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-28 21:02:32,625 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 21:02:32,626 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 21:02:32,829 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 21:02:32,829 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 21:02:32,829 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-28 21:02:32,830 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-28 21:02:32,830 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-28 21:02:32,831 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-28 21:02:32,831 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-28 21:02:32,831 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-28 21:02:32,832 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-28 21:02:32,832 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-28 21:02:32,832 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-28 21:02:32,832 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-28 21:02:32,833 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-28 21:02:32,833 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-28 21:02:32,833 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-28 21:02:32,833 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-28 21:02:32,833 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-28 21:02:32,833 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-28 21:02:32,835 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-28 21:02:32,835 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-28 21:02:32,835 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-28 21:02:32,835 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-28 21:02:32,836 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-28 21:02:32,836 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 21:02:32,836 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 21:02:33,043 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 21:02:33,044 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 21:02:33,044 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 21:02:33,046 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 21:02:33,046 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 21:02:33,177 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 21:02:33,178 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 21:02:33,178 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-28 21:02:33,179 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-28 21:02:33,179 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-28 21:02:33,179 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-28 21:02:33,180 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-28 21:02:33,180 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-28 21:02:33,180 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-28 21:02:33,180 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-28 21:02:33,180 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-28 21:02:33,181 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-28 21:02:33,181 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-28 21:02:33,181 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-28 21:02:33,181 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-28 21:02:33,181 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-28 21:02:33,182 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-28 21:02:33,182 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-28 21:02:33,182 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-28 21:02:33,183 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-28 21:02:33,183 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-28 21:02:33,183 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-28 21:02:33,183 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-28 21:02:33,185 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 21:02:33,185 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 21:02:33,342 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 21:02:33,342 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 21:02:33,342 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-28 21:02:33,343 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-28 21:02:33,343 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-28 21:02:33,343 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-28 21:02:33,343 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-28 21:02:33,344 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-28 21:02:33,344 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-28 21:02:33,344 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-28 21:02:33,344 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-28 21:02:33,344 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-28 21:02:33,345 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-28 21:02:33,345 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-28 21:02:33,345 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-28 21:02:33,345 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-28 21:02:33,345 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-28 21:02:33,346 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-28 21:02:33,346 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-28 21:02:33,347 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-28 21:02:33,347 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-28 21:02:33,347 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-28 21:02:33,347 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-28 21:03:43,108 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 21:03:43,109 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 21:03:43,109 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 21:03:43,109 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-28 21:03:43,110 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-28 21:03:43,111 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 21:03:43,112 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 21:03:43,459 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 21:03:43,459 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 21:03:43,460 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-28 21:03:43,463 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 21:03:43,463 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 21:03:43,649 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 21:03:43,650 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 21:03:43,651 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 21:03:43,753 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-28 21:03:43,754 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 21:03:43,754 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 21:03:45,095 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 21:03:45,095 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 21:03:45,096 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-28 21:03:45,096 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-28 21:03:45,096 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-28 21:03:45,097 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-28 21:03:45,097 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-28 21:03:45,097 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-28 21:03:45,097 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-28 21:03:45,098 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-28 21:03:45,098 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-28 21:03:45,098 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-28 21:03:45,098 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-28 21:03:45,099 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-28 21:03:45,099 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-28 21:03:45,099 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-28 21:03:45,100 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-28 21:03:45,100 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-28 21:03:45,100 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-28 21:03:45,101 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-28 21:03:45,101 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-28 21:03:45,101 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-28 21:03:45,102 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-28 21:03:45,102 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 21:03:45,102 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 21:03:45,443 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 21:03:45,444 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 21:03:45,444 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 21:03:45,446 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 21:03:45,446 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 21:03:45,622 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 21:03:45,623 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 21:03:45,623 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-28 21:03:45,624 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-28 21:03:45,624 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-28 21:03:45,624 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-28 21:03:45,625 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-28 21:03:45,625 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-28 21:03:45,625 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-28 21:03:45,625 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-28 21:03:45,625 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-28 21:03:45,626 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-28 21:03:45,626 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-28 21:03:45,626 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-28 21:03:45,626 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-28 21:03:45,626 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-28 21:03:45,626 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-28 21:03:45,627 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-28 21:03:45,628 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-28 21:03:45,629 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-28 21:03:45,629 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-28 21:03:45,629 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-28 21:03:45,630 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-28 21:03:45,632 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 21:03:45,633 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 21:03:45,741 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 21:03:45,742 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 21:03:45,742 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-28 21:03:45,743 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-28 21:03:45,743 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-28 21:03:45,743 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-28 21:03:45,744 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-28 21:03:45,744 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-28 21:03:45,744 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-28 21:03:45,744 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-28 21:03:45,745 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-28 21:03:45,745 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-28 21:03:45,745 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-28 21:03:45,746 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-28 21:03:45,746 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-28 21:03:45,746 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-28 21:03:45,746 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-28 21:03:45,746 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-28 21:03:45,747 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-28 21:03:45,747 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-28 21:03:45,747 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-28 21:03:45,747 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-28 21:03:45,748 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-28 21:18:44,738 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 21:18:44,740 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 21:18:44,740 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 21:18:44,741 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 21:18:44,741 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 21:18:45,378 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 21:18:45,378 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 21:18:45,380 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 21:18:45,725 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:63 - find_git_executable - 找到Git可执行文件: git
2025-05-28 21:18:45,737 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:73 - __init__ - LLM重试处理器初始化完成
2025-05-28 21:18:45,741 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:89 - __init__ - ConversationLogger initialized, log_dir: E:\Projects\aider-plus\logs\conversations
2025-05-28 21:18:45,743 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-28 21:18:45,744 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-28 21:18:45,745 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 21:18:45,746 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 21:18:46,197 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 21:18:46,198 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 21:18:46,198 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-28 21:18:46,199 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-28 21:18:46,199 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 21:18:46,199 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 21:18:46,401 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 21:18:46,401 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 21:18:46,401 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-28 21:18:46,401 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-28 21:18:46,402 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-28 21:18:46,402 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-28 21:18:46,402 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-28 21:18:46,402 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-28 21:18:46,403 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-28 21:18:46,403 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-28 21:18:46,403 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-28 21:18:46,403 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-28 21:18:46,403 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-28 21:18:46,403 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-28 21:18:46,404 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-28 21:18:46,404 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-28 21:18:46,404 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-28 21:18:46,404 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-28 21:18:46,405 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-28 21:18:46,405 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-28 21:18:46,405 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-28 21:18:46,406 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-28 21:18:46,410 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-28 21:46:25,215 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 21:46:25,216 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 21:46:25,216 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 21:46:25,216 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\Projects\aider-plus
2025-05-28 21:46:25,216 - bot_agent.tools.project_context_collector - ERROR - project_context_collector.py:82 - collect_full_context - 项目上下文收集失败: cannot import name 'InformationQueryTool' from 'bot_agent.tools.information_query_tools' (E:\Projects\aider-plus\bot_agent\tools\information_query_tools.py)
2025-05-28 21:46:25,773 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 21:46:25,773 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 21:46:26,587 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 21:46:26,588 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 21:46:26,589 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 21:46:26,690 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 21:46:26,691 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 21:46:27,126 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 21:46:27,127 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 21:46:27,127 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 21:49:54,997 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 21:49:54,998 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 21:49:54,998 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 21:49:54,999 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\Projects\aider-plus
2025-05-28 21:49:59,218 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-28 21:49:59,218 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-28 21:50:01,770 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-28 21:50:01,771 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-28 21:50:01,771 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-28 21:50:01,772 - bot_agent.tools.project_context_collector - ERROR - project_context_collector.py:200 - format_context_for_ai - 格式化上下文失败: 'ToolResult' object has no attribute 'get'
2025-05-28 21:50:02,272 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 21:50:02,272 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 21:50:03,499 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 21:50:03,500 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 21:50:03,501 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 21:50:03,602 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 21:50:03,602 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 21:50:03,812 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 21:50:03,813 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 21:50:03,813 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 21:53:43,068 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 21:53:43,068 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 21:53:43,069 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 21:53:43,069 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\Projects\aider-plus
2025-05-28 21:53:47,341 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-28 21:53:47,342 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-28 21:53:49,911 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-28 21:53:49,911 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-28 21:53:49,912 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-28 21:53:50,282 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 21:53:50,282 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 21:53:53,482 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 21:53:53,482 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 21:53:53,483 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 21:53:53,584 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 21:53:53,585 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 21:53:56,828 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 21:53:56,829 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 21:53:56,829 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-28 21:56:37,682 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 21:56:37,683 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 21:56:37,684 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 21:56:37,684 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\Projects\aider-plus
2025-05-28 21:56:41,905 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-28 21:56:41,905 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-28 21:56:44,660 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-28 21:56:44,660 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-28 21:56:44,661 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-28 21:56:44,665 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: C:\Users\<USER>\AppData\Local\Temp\tmphwxx90_w
2025-05-28 21:56:48,797 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-28 21:56:48,798 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-28 21:56:51,434 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-28 21:56:51,435 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-28 21:56:51,435 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-28 21:56:51,441 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: C:\Users\<USER>\AppData\Local\Temp\tmpqjyywfv2
2025-05-28 21:56:55,148 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-28 21:56:55,149 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-28 21:56:57,678 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-28 21:56:57,678 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-28 21:56:57,679 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-28 21:56:57,681 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\Projects\aider-plus
2025-05-28 21:57:01,693 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-28 21:57:01,694 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-28 21:57:04,331 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-28 21:57:04,331 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-28 21:57:04,331 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-28 21:57:04,332 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\Projects\aider-plus
2025-05-28 21:57:08,246 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-28 21:57:08,247 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-28 21:57:10,950 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-28 21:57:10,951 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-28 21:57:10,951 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-28 21:57:10,951 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\Projects\aider-plus
2025-05-28 21:57:15,011 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-28 21:57:15,011 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-28 21:57:17,802 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-28 21:57:17,802 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-28 21:57:17,803 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-28 21:57:17,804 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: C:\Users\<USER>\AppData\Local\Temp\tmpdw0rd60e
2025-05-28 21:57:21,878 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-28 21:57:21,879 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-28 21:57:24,460 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-28 21:57:24,461 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-28 21:57:24,461 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-28 22:00:38,567 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 22:00:38,569 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 22:00:38,569 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 22:00:38,570 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 22:00:38,570 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 22:00:39,368 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 22:00:39,369 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 22:00:39,370 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:53 - __init__ - JobFailureAnalyzer initialized
2025-05-28 22:00:39,371 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-28 22:00:39,372 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-28 22:00:39,509 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-28 22:00:39,509 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 22:00:39,510 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:53 - __init__ - JobFailureAnalyzer initialized
2025-05-28 22:00:39,510 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:136 - _make_request - Making GET request to http://***************/api/v4/projects/3
2025-05-28 22:00:39,686 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:155 - _make_request - Response status code: 401
2025-05-28 22:00:39,686 - bot_agent.clients.gitlab_client - DEBUG - gitlab_client.py:160 - _make_request - Response content preview: b'{"message":"401 Unauthorized"}'
2025-05-28 22:00:39,686 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:174 - _make_request - GitLab API request failed (attempt 1/2): 401 Client Error: Unauthorized for url: http://***************/api/v4/projects/3
2025-05-28 22:00:39,690 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:181 - _make_request - Error response status code: 401
2025-05-28 22:00:39,691 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:185 - _make_request - Error response data: {'message': '401 Unauthorized'}
2025-05-28 22:00:39,691 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:193 - _make_request - 认证/权限错误 (401)，不进行重试
2025-05-28 22:00:39,692 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:208 - _make_request - 客户端错误，API仍然可用: 401 Client Error: Unauthorized for url: http://***************/api/v4/projects/3
2025-05-28 22:00:39,692 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:213 - _make_request - Request to projects/3 failed, returning None due to fallback mode
2025-05-28 22:00:39,693 - bot_agent.handlers.job_failure_analyzer - WARNING - job_failure_analyzer.py:779 - _get_project_directory - 无法获取项目ID 3 的信息，使用当前目录
2025-05-28 22:00:39,694 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:136 - _make_request - Making GET request to http://***************/api/v4/projects/999
2025-05-28 22:00:39,842 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:155 - _make_request - Response status code: 401
2025-05-28 22:00:39,842 - bot_agent.clients.gitlab_client - DEBUG - gitlab_client.py:160 - _make_request - Response content preview: b'{"message":"401 Unauthorized"}'
2025-05-28 22:00:39,843 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:174 - _make_request - GitLab API request failed (attempt 1/2): 401 Client Error: Unauthorized for url: http://***************/api/v4/projects/999
2025-05-28 22:00:39,843 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:181 - _make_request - Error response status code: 401
2025-05-28 22:00:39,843 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:185 - _make_request - Error response data: {'message': '401 Unauthorized'}
2025-05-28 22:00:39,844 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:193 - _make_request - 认证/权限错误 (401)，不进行重试
2025-05-28 22:00:39,844 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:208 - _make_request - 客户端错误，API仍然可用: 401 Client Error: Unauthorized for url: http://***************/api/v4/projects/999
2025-05-28 22:00:39,844 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:213 - _make_request - Request to projects/999 failed, returning None due to fallback mode
2025-05-28 22:00:39,845 - bot_agent.handlers.job_failure_analyzer - WARNING - job_failure_analyzer.py:779 - _get_project_directory - 无法获取项目ID 999 的信息，使用当前目录
2025-05-28 22:00:39,849 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\Projects\aider-plus
2025-05-28 22:00:43,994 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-28 22:00:43,994 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-28 22:00:46,890 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-28 22:00:46,891 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-28 22:00:46,891 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-28 22:00:46,895 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\aider-git-repos\aider-plus
2025-05-28 22:00:51,121 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-28 22:00:51,122 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-28 22:00:53,767 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-28 22:00:53,768 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-28 22:00:53,768 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-28 22:00:53,768 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\aider-git-repos\ai-proxy
2025-05-28 22:00:57,584 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-28 22:00:57,585 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-28 22:01:00,527 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-28 22:01:00,527 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-28 22:01:00,528 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 08:58:13,309 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 08:58:13,309 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 08:58:13,309 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 08:58:13,310 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-29 08:58:13,310 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-29 08:58:13,311 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 08:58:13,311 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 08:58:13,657 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 08:58:13,657 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 08:58:13,658 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-29 08:58:13,665 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-29 08:58:13,665 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 08:58:13,665 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 08:58:13,862 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 08:58:13,863 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 08:58:13,864 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-29 08:58:13,864 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-29 08:58:13,865 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-29 08:58:13,866 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-29 08:58:13,866 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-29 08:58:13,866 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-29 08:58:13,866 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-29 08:58:13,866 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-29 08:58:13,867 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-29 08:58:13,867 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-29 08:58:13,867 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-29 08:58:13,867 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-29 08:58:13,867 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-29 08:58:13,868 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-29 08:58:13,868 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-29 08:58:13,868 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-29 08:58:13,869 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-29 08:58:13,869 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-29 08:58:13,869 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-29 08:58:13,869 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-29 08:58:13,870 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-29 08:58:13,871 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:120 - start_session - Started conversation session: test_enhanced_logging_1748480293
2025-05-29 08:58:13,872 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 1 for session test_enhanced_logging_1748480293
2025-05-29 08:58:13,874 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 2 for session test_enhanced_logging_1748480293
2025-05-29 08:58:13,875 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 3 for session test_enhanced_logging_1748480293
2025-05-29 08:58:13,877 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 4 for session test_enhanced_logging_1748480293
2025-05-29 08:58:13,879 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 5 for session test_enhanced_logging_1748480293
2025-05-29 08:58:13,880 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:208 - end_session - Ended conversation session: test_enhanced_logging_1748480293, status: ConversationStatus.SUCCESS
2025-05-29 08:59:50,204 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 08:59:50,205 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 08:59:50,205 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 08:59:50,206 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-29 08:59:50,206 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-29 08:59:50,207 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 08:59:50,208 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 08:59:50,840 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 08:59:50,841 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 08:59:50,841 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-29 08:59:50,842 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-29 08:59:50,842 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 08:59:50,842 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 08:59:51,064 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 08:59:51,064 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 08:59:51,064 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-29 08:59:51,065 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-29 08:59:51,065 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-29 08:59:51,066 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-29 08:59:51,067 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-29 08:59:51,067 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-29 08:59:51,067 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-29 08:59:51,067 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-29 08:59:51,067 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-29 08:59:51,067 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-29 08:59:51,067 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-29 08:59:51,068 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-29 08:59:51,068 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-29 08:59:51,069 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-29 08:59:51,069 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-29 08:59:51,069 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-29 08:59:51,070 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-29 08:59:51,071 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-29 08:59:51,071 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-29 08:59:51,071 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-29 08:59:51,071 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-29 08:59:51,072 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 08:59:51,072 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 08:59:51,249 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 08:59:51,249 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 08:59:51,250 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-29 08:59:51,250 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-29 08:59:51,251 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-29 08:59:51,251 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-29 08:59:51,251 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-29 08:59:51,251 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-29 08:59:51,251 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-29 08:59:51,251 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-29 08:59:51,252 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-29 08:59:51,252 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-29 08:59:51,252 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-29 08:59:51,253 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-29 08:59:51,253 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-29 08:59:51,253 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-29 08:59:51,253 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-29 08:59:51,253 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-29 08:59:51,254 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-29 08:59:51,254 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-29 08:59:51,254 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-29 08:59:51,254 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-29 08:59:51,254 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-29 08:59:51,255 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:120 - start_session - Started conversation session: test_enhanced_logging_1748480391
2025-05-29 08:59:51,257 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 1 for session test_enhanced_logging_1748480391
2025-05-29 08:59:51,259 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 2 for session test_enhanced_logging_1748480391
2025-05-29 08:59:51,260 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 3 for session test_enhanced_logging_1748480391
2025-05-29 08:59:51,261 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 4 for session test_enhanced_logging_1748480391
2025-05-29 08:59:51,262 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 5 for session test_enhanced_logging_1748480391
2025-05-29 08:59:51,265 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:208 - end_session - Ended conversation session: test_enhanced_logging_1748480391, status: ConversationStatus.SUCCESS
2025-05-29 09:00:42,409 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 09:00:42,410 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 09:00:42,410 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 09:00:42,411 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-29 09:00:42,411 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-29 09:00:42,413 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 09:00:42,413 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 09:00:42,759 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 09:00:42,760 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 09:00:42,760 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-29 09:00:42,761 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-29 09:00:42,761 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 09:00:42,762 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 09:00:42,888 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 09:00:42,888 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 09:00:42,889 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-29 09:00:42,889 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-29 09:00:42,890 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-29 09:00:42,890 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-29 09:00:42,891 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-29 09:00:42,891 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-29 09:00:42,891 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-29 09:00:42,891 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-29 09:00:42,892 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-29 09:00:42,892 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-29 09:00:42,892 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-29 09:00:42,892 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-29 09:00:42,892 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-29 09:00:42,893 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-29 09:00:42,893 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-29 09:00:42,894 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-29 09:00:42,894 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-29 09:00:42,894 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-29 09:00:42,895 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-29 09:00:42,895 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-29 09:00:42,895 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-29 09:00:42,896 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 09:00:42,897 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 09:00:43,066 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 09:00:43,067 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 09:00:43,067 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-29 09:00:43,068 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-29 09:00:43,068 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-29 09:00:43,069 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-29 09:00:43,069 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-29 09:00:43,069 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-29 09:00:43,069 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-29 09:00:43,069 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-29 09:00:43,070 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-29 09:00:43,070 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-29 09:00:43,070 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-29 09:00:43,070 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-29 09:00:43,070 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-29 09:00:43,070 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-29 09:00:43,071 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-29 09:00:43,071 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-29 09:00:43,072 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-29 09:00:43,072 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-29 09:00:43,073 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-29 09:00:43,073 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-29 09:00:43,073 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-29 09:00:43,074 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:120 - start_session - Started conversation session: test_enhanced_logging_1748480443
2025-05-29 09:00:43,076 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 1 for session test_enhanced_logging_1748480443
2025-05-29 09:00:43,078 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 2 for session test_enhanced_logging_1748480443
2025-05-29 09:00:43,080 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 3 for session test_enhanced_logging_1748480443
2025-05-29 09:00:43,081 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 4 for session test_enhanced_logging_1748480443
2025-05-29 09:00:43,082 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 5 for session test_enhanced_logging_1748480443
2025-05-29 09:00:43,084 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:208 - end_session - Ended conversation session: test_enhanced_logging_1748480443, status: ConversationStatus.SUCCESS
2025-05-29 09:01:12,092 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 09:01:12,092 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 09:01:12,093 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 09:01:12,094 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-29 09:01:12,094 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-29 09:01:12,096 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 09:01:12,096 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 09:01:12,671 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 09:01:12,671 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 09:01:12,672 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-29 09:01:12,672 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-29 09:01:12,672 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 09:01:12,673 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 09:01:12,968 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 09:01:12,969 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 09:01:12,970 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-29 09:01:12,970 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-29 09:01:12,971 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-29 09:01:12,972 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-29 09:01:12,972 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-29 09:01:12,972 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-29 09:01:12,973 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-29 09:01:12,973 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-29 09:01:12,973 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-29 09:01:12,973 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-29 09:01:12,974 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-29 09:01:12,974 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-29 09:01:12,974 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-29 09:01:12,975 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-29 09:01:12,975 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-29 09:01:12,975 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-29 09:01:12,976 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-29 09:01:12,976 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-29 09:01:12,977 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-29 09:01:12,977 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-29 09:01:12,977 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-29 09:01:36,796 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 09:01:36,797 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 09:01:36,797 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 09:01:36,798 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-29 09:01:36,798 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-29 09:01:36,799 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 09:01:36,799 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 09:01:37,379 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 09:01:37,380 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 09:01:37,381 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-29 09:01:37,381 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-29 09:01:37,381 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 09:01:37,382 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 09:01:37,548 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 09:01:37,548 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 09:01:37,548 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-29 09:01:37,550 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-29 09:01:37,550 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-29 09:01:37,552 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-29 09:01:37,552 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-29 09:01:37,552 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-29 09:01:37,552 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-29 09:01:37,553 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-29 09:01:37,554 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-29 09:01:37,554 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-29 09:01:37,554 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-29 09:01:37,555 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-29 09:01:37,555 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-29 09:01:37,555 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-29 09:01:37,555 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-29 09:01:37,556 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-29 09:01:37,556 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-29 09:01:37,557 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-29 09:01:37,557 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-29 09:01:37,557 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-29 09:01:37,557 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-29 09:02:06,854 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 09:02:06,855 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 09:02:06,855 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 09:02:06,856 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-29 09:02:06,856 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-29 09:02:06,857 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 09:02:06,858 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 09:02:07,662 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 09:02:07,662 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 09:02:07,662 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-29 09:02:07,663 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-29 09:02:07,663 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 09:02:07,664 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 09:02:07,770 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 09:02:07,771 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 09:02:07,771 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-29 09:02:07,771 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-29 09:02:07,772 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-29 09:02:07,773 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-29 09:02:07,773 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-29 09:02:07,773 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-29 09:02:07,773 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-29 09:02:07,774 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-29 09:02:07,774 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-29 09:02:07,774 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-29 09:02:07,774 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-29 09:02:07,775 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-29 09:02:07,775 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-29 09:02:07,775 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-29 09:02:07,775 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-29 09:02:07,776 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-29 09:02:07,777 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-29 09:02:07,777 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-29 09:02:07,777 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-29 09:02:07,778 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-29 09:02:07,778 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-29 09:02:07,779 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 09:02:07,779 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 09:02:07,918 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 09:02:07,919 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 09:02:07,919 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-29 09:02:07,920 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-29 09:02:07,920 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-29 09:02:07,921 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-29 09:02:07,921 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-29 09:02:07,921 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-29 09:02:07,921 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-29 09:02:07,921 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-29 09:02:07,921 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-29 09:02:07,922 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-29 09:02:07,922 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-29 09:02:07,922 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-29 09:02:07,922 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-29 09:02:07,923 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-29 09:02:07,923 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-29 09:02:07,924 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-29 09:02:07,924 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-29 09:02:07,924 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-29 09:02:07,925 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-29 09:02:07,925 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-29 09:02:07,925 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-29 09:02:07,926 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:120 - start_session - Started conversation session: test_enhanced_logging_1748480527
2025-05-29 09:02:07,927 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 1 for session test_enhanced_logging_1748480527
2025-05-29 09:02:07,929 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 2 for session test_enhanced_logging_1748480527
2025-05-29 09:02:07,931 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 3 for session test_enhanced_logging_1748480527
2025-05-29 09:02:07,932 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 4 for session test_enhanced_logging_1748480527
2025-05-29 09:02:07,933 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 5 for session test_enhanced_logging_1748480527
2025-05-29 09:02:07,934 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:208 - end_session - Ended conversation session: test_enhanced_logging_1748480527, status: ConversationStatus.SUCCESS
2025-05-29 10:02:15,374 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 10:02:15,375 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 10:02:15,375 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 10:02:15,376 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 10:02:15,376 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 10:02:15,851 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 10:02:15,851 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 10:02:15,852 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 10:02:15,955 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:254 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 10:02:15,957 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:259 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 10:02:15,957 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\Projects\aider-plus
2025-05-29 10:02:19,984 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 10:02:19,984 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 10:02:22,503 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 10:02:22,503 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 10:02:22,504 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 10:02:22,504 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:435 - _ai_generate_fix_plan - AI修复方案生成异常: 'ToolResult' object has no attribute 'get'
2025-05-29 10:02:22,505 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:176 - execute_targeted_fixes - 🤖 开始AI驱动的智能修复...
2025-05-29 10:02:22,505 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:254 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 10:02:22,505 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:259 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 10:02:22,506 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\Projects\aider-plus
2025-05-29 10:02:26,293 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 10:02:26,293 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 10:02:28,823 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 10:02:28,823 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 10:02:28,823 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 10:02:28,823 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:435 - _ai_generate_fix_plan - AI修复方案生成异常: 'ToolResult' object has no attribute 'get'
2025-05-29 10:02:28,823 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:235 - execute_targeted_fixes - AI修复方案生成失败，回退到策略修复
2025-05-29 10:02:28,823 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:813 - _fallback_strategy_fixes - 🔄 回退到策略修复模式...
2025-05-29 10:02:28,823 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:819 - _fallback_strategy_fixes - 🔧 处理 config_errors 类型错误: 1 个
2025-05-29 10:02:28,823 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:835 - _fallback_strategy_fixes - 修复结果: {'success': True, 'message': '尝试修复 1 个通用错误'}
2025-05-29 10:02:28,824 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:819 - _fallback_strategy_fixes - 🔧 处理 dependency_errors 类型错误: 1 个
2025-05-29 10:02:28,824 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1230 - _fix_dependency_errors - 🔧 修复依赖错误: 1 个
2025-05-29 10:02:28,824 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:835 - _fallback_strategy_fixes - 修复结果: {'success': False, 'message': '修复了 0/1 个依赖错误', 'details': [], 'fixed_count': 0}
2025-05-29 10:05:07,761 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 10:05:07,761 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 10:05:07,762 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 10:05:07,763 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 10:05:07,763 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 10:05:08,616 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 10:05:08,616 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 10:05:08,618 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 10:05:08,719 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:176 - execute_targeted_fixes - 🤖 开始AI驱动的智能修复...
2025-05-29 10:05:08,719 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:254 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 10:05:08,723 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:259 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 10:05:08,724 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\Projects\aider-plus
2025-05-29 10:05:12,750 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 10:05:12,751 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 10:05:15,611 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 10:05:15,611 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 10:05:15,612 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 10:05:15,612 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:435 - _ai_generate_fix_plan - AI修复方案生成异常: 'ToolResult' object has no attribute 'get'
2025-05-29 10:05:15,612 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:235 - execute_targeted_fixes - AI修复方案生成失败，回退到策略修复
2025-05-29 10:05:15,612 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:813 - _fallback_strategy_fixes - 🔄 回退到策略修复模式...
2025-05-29 10:05:15,612 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:819 - _fallback_strategy_fixes - 🔧 处理 config_errors 类型错误: 2 个
2025-05-29 10:05:15,613 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:835 - _fallback_strategy_fixes - 修复结果: {'success': True, 'message': '尝试修复 2 个通用错误'}
2025-05-29 10:05:15,613 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:819 - _fallback_strategy_fixes - 🔧 处理 lint_errors 类型错误: 2 个
2025-05-29 10:05:15,613 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1293 - _fix_lint_errors - 🔧 修复代码规范错误: 2 个
2025-05-29 10:05:15,613 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:835 - _fallback_strategy_fixes - 修复结果: {'success': False, 'message': '修复了 0/2 个代码规范错误', 'details': [], 'fixed_count': 0}
2025-05-29 10:05:15,614 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\Projects\aider-plus
2025-05-29 10:05:19,632 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 10:05:19,632 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 10:05:22,215 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 10:05:22,216 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 10:05:22,216 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 10:05:22,217 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:254 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 10:05:22,217 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:259 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 10:05:22,217 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\Projects\aider-plus
2025-05-29 10:05:26,158 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 10:05:26,158 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 10:05:28,756 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 10:05:28,757 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 10:05:28,757 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 10:05:28,757 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:435 - _ai_generate_fix_plan - AI修复方案生成异常: 'ToolResult' object has no attribute 'get'
2025-05-29 10:09:50,999 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 10:09:51,000 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 10:09:51,000 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 10:09:51,002 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 10:09:51,003 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 10:09:51,489 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 10:09:51,489 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 10:09:51,491 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 10:09:51,592 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:254 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 10:09:51,595 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:259 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 10:09:51,595 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\Projects\aider-plus
2025-05-29 10:09:55,562 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 10:09:55,563 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 10:09:58,084 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 10:09:58,085 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 10:09:58,085 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 10:09:58,085 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:435 - _ai_generate_fix_plan - AI修复方案生成异常: 'ToolResult' object has no attribute 'get'
2025-05-29 10:09:58,086 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:176 - execute_targeted_fixes - 🤖 开始AI驱动的智能修复...
2025-05-29 10:09:58,086 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:254 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 10:09:58,086 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:259 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 10:09:58,087 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\Projects\aider-plus
2025-05-29 10:10:01,957 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 10:10:01,958 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 10:10:04,513 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 10:10:04,513 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 10:10:04,513 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 10:10:04,514 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:435 - _ai_generate_fix_plan - AI修复方案生成异常: 'ToolResult' object has no attribute 'get'
2025-05-29 10:10:04,514 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:235 - execute_targeted_fixes - AI修复方案生成失败，回退到策略修复
2025-05-29 10:10:04,514 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:813 - _fallback_strategy_fixes - 🔄 回退到策略修复模式...
2025-05-29 10:10:04,514 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:819 - _fallback_strategy_fixes - 🔧 处理 config_errors 类型错误: 1 个
2025-05-29 10:10:04,515 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:835 - _fallback_strategy_fixes - 修复结果: {'success': True, 'message': '尝试修复 1 个通用错误'}
2025-05-29 10:10:04,515 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:819 - _fallback_strategy_fixes - 🔧 处理 lint_errors 类型错误: 1 个
2025-05-29 10:10:04,515 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1293 - _fix_lint_errors - 🔧 修复代码规范错误: 1 个
2025-05-29 10:10:04,515 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:835 - _fallback_strategy_fixes - 修复结果: {'success': False, 'message': '修复了 0/1 个代码规范错误', 'details': [], 'fixed_count': 0}
2025-05-29 10:10:04,515 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\Projects\aider-plus
2025-05-29 10:10:08,345 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 10:10:08,345 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 10:10:10,895 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 10:10:10,896 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 10:10:10,896 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 10:12:30,659 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 10:12:30,659 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 10:12:30,660 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 10:12:30,661 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 10:12:30,662 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 10:12:31,003 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 10:12:31,004 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 10:12:31,005 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 10:12:31,107 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:254 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 10:12:31,108 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:259 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 10:12:31,109 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\Projects\aider-plus
2025-05-29 10:12:34,990 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 10:12:34,990 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 10:12:37,598 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 10:12:37,598 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 10:12:37,598 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 10:12:37,598 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:450 - _ai_generate_fix_plan - AI修复方案生成异常: 'ToolResult' object has no attribute 'get'
2025-05-29 10:12:37,599 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:176 - execute_targeted_fixes - 🤖 开始AI驱动的智能修复...
2025-05-29 10:12:37,599 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:254 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 10:12:37,600 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:259 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 10:12:37,600 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\Projects\aider-plus
2025-05-29 10:12:41,476 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 10:12:41,476 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 10:12:43,972 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 10:12:43,972 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 10:12:43,972 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 10:12:43,972 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:450 - _ai_generate_fix_plan - AI修复方案生成异常: 'ToolResult' object has no attribute 'get'
2025-05-29 10:12:43,973 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:235 - execute_targeted_fixes - AI修复方案生成失败，回退到策略修复
2025-05-29 10:12:43,973 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:836 - _fallback_strategy_fixes - 🔄 回退到策略修复模式...
2025-05-29 10:12:43,973 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:842 - _fallback_strategy_fixes - 🔧 处理 config_errors 类型错误: 1 个
2025-05-29 10:12:43,973 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:858 - _fallback_strategy_fixes - 修复结果: {'success': True, 'message': '尝试修复 1 个通用错误'}
2025-05-29 10:12:43,973 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:842 - _fallback_strategy_fixes - 🔧 处理 lint_errors 类型错误: 1 个
2025-05-29 10:12:43,974 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1316 - _fix_lint_errors - 🔧 修复代码规范错误: 1 个
2025-05-29 10:12:43,974 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:858 - _fallback_strategy_fixes - 修复结果: {'success': False, 'message': '修复了 0/1 个代码规范错误', 'details': [], 'fixed_count': 0}
2025-05-29 10:17:59,813 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 10:17:59,814 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 10:17:59,814 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 10:17:59,815 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 10:17:59,816 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 10:18:00,194 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 10:18:00,194 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 10:18:00,195 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 10:18:00,296 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:254 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 10:18:00,303 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:259 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 10:18:00,303 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\Projects\aider-plus
2025-05-29 10:18:04,279 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 10:18:04,279 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 10:18:06,870 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 10:18:06,871 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 10:18:06,871 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 10:18:06,872 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:450 - _ai_generate_fix_plan - AI修复方案生成异常: 'ToolResult' object has no attribute 'get'
2025-05-29 10:18:06,872 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\Projects\aider-plus
2025-05-29 10:18:10,738 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 10:18:10,738 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 10:18:13,259 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 10:18:13,260 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 10:18:13,260 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 10:20:59,486 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 10:20:59,487 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 10:20:59,487 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 10:20:59,488 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 10:20:59,488 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 10:20:59,837 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 10:20:59,838 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 10:20:59,840 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 10:20:59,941 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:254 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 10:20:59,944 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:259 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 10:20:59,944 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\Projects\aider-plus
2025-05-29 10:21:03,878 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 10:21:03,879 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 10:21:06,458 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 10:21:06,458 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 10:21:06,458 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 10:21:06,459 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:450 - _ai_generate_fix_plan - AI修复方案生成异常: 'ToolResult' object has no attribute 'get'
2025-05-29 10:21:06,460 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\Projects\aider-plus
2025-05-29 10:21:10,251 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 10:21:10,251 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 10:21:12,859 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 10:21:12,860 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 10:21:12,861 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 10:37:12,889 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 10:37:12,890 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 10:37:12,890 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 10:37:12,891 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 10:37:12,892 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 10:37:14,321 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 10:37:14,322 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 10:37:14,324 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 10:37:14,425 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 10:37:14,425 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 10:37:14,920 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 10:37:14,921 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 10:37:14,921 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 10:37:14,926 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:89 - __init__ - ConversationLogger initialized, log_dir: E:\Projects\aider-plus\logs\conversations
2025-05-29 10:37:14,927 - bot_agent.utils.conversation_logger - WARNING - conversation_logger.py:147 - log_round - Session test_round_numbering_fix not found
2025-05-29 10:37:14,927 - bot_agent.utils.conversation_logger - WARNING - conversation_logger.py:147 - log_round - Session test_round_numbering_fix not found
2025-05-29 10:37:14,927 - bot_agent.utils.conversation_logger - WARNING - conversation_logger.py:147 - log_round - Session test_round_numbering_fix not found
2025-05-29 10:37:14,927 - bot_agent.utils.conversation_logger - WARNING - conversation_logger.py:147 - log_round - Session test_round_numbering_fix not found
2025-05-29 10:37:14,928 - bot_agent.utils.conversation_logger - WARNING - conversation_logger.py:147 - log_round - Session test_round_numbering_fix not found
2025-05-29 10:37:14,929 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 10:37:14,929 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 10:37:15,122 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 10:37:15,123 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 10:37:15,124 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 10:38:15,375 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 10:38:15,376 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 10:38:15,376 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 10:38:15,378 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 10:38:15,378 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 10:38:15,657 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 10:38:15,657 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 10:38:15,658 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 10:38:15,759 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 10:38:15,760 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 10:38:15,959 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 10:38:15,959 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 10:38:15,960 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 10:38:15,964 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:89 - __init__ - ConversationLogger initialized, log_dir: E:\Projects\aider-plus\logs\conversations
2025-05-29 10:38:15,965 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:120 - start_session - Started conversation session: test_task_1748486295
2025-05-29 10:38:15,967 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 1 for session test_task_1748486295
2025-05-29 10:38:15,968 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 2 for session test_task_1748486295
2025-05-29 10:38:15,969 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 3 for session test_task_1748486295
2025-05-29 10:38:15,970 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 4 for session test_task_1748486295
2025-05-29 10:38:15,971 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 5 for session test_task_1748486295
2025-05-29 10:38:15,973 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:208 - end_session - Ended conversation session: test_task_1748486295, status: ConversationStatus.SUCCESS
2025-05-29 10:38:15,986 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 10:38:15,986 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 10:38:16,568 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 10:38:16,569 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 10:38:16,569 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:02:18,400 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 11:02:18,400 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 11:02:18,401 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 11:02:18,402 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:02:18,403 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:02:19,345 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:02:19,345 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:02:19,346 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:02:19,448 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:02:19,448 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:02:19,955 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:02:19,956 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:02:19,957 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:02:19,960 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:89 - __init__ - ConversationLogger initialized, log_dir: E:\Projects\aider-plus\logs\conversations
2025-05-29 11:02:19,960 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:120 - start_session - Started conversation session: test_task_1748487739
2025-05-29 11:02:19,962 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 1 for session test_task_1748487739
2025-05-29 11:02:19,963 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 2 for session test_task_1748487739
2025-05-29 11:02:19,964 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 3 for session test_task_1748487739
2025-05-29 11:02:19,965 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 4 for session test_task_1748487739
2025-05-29 11:02:19,966 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:172 - log_round - Logged round 5 for session test_task_1748487739
2025-05-29 11:02:19,967 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:208 - end_session - Ended conversation session: test_task_1748487739, status: ConversationStatus.SUCCESS
2025-05-29 11:02:19,982 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:02:19,984 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:02:20,192 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:02:20,193 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:02:20,193 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:02:20,195 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:02:20,195 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:02:20,371 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:02:20,372 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:02:20,372 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:27:30,686 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 11:27:30,686 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 11:27:30,687 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 11:27:30,688 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:27:30,688 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:27:31,409 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:27:31,410 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:27:31,411 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:27:31,512 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:27:31,512 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:27:31,700 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:27:31,701 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:27:31,701 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:27:31,710 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:97 - __init__ - ConversationLogger initialized, log_dir: E:\Projects\aider-plus\logs\conversations
2025-05-29 11:27:31,711 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:132 - start_session - Started conversation session: test_task_1748489251
2025-05-29 11:27:31,713 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 1 for session test_task_1748489251
2025-05-29 11:27:31,715 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 2 for session test_task_1748489251
2025-05-29 11:27:31,716 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 3 for session test_task_1748489251
2025-05-29 11:27:31,717 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 4 for session test_task_1748489251
2025-05-29 11:27:31,718 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 5 for session test_task_1748489251
2025-05-29 11:27:31,719 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:246 - end_session - Ended conversation session: test_task_1748489251, status: ConversationStatus.SUCCESS
2025-05-29 11:27:31,728 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:27:31,728 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:27:32,109 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:27:32,109 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:27:32,110 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:27:32,110 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:27:32,110 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:27:32,314 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:27:32,315 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:27:32,315 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:27:32,316 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:27:32,316 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:27:32,832 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:27:32,833 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:27:32,833 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:34:21,274 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 11:34:21,274 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 11:34:21,275 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 11:34:21,276 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:34:21,276 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:34:21,600 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:34:21,601 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:34:21,601 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:34:21,703 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:34:21,703 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:34:22,258 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:34:22,258 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:34:22,259 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:34:22,262 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:97 - __init__ - ConversationLogger initialized, log_dir: E:\Projects\aider-plus\logs\conversations
2025-05-29 11:34:22,262 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:132 - start_session - Started conversation session: test_task_1748489662
2025-05-29 11:34:22,264 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 1 for session test_task_1748489662
2025-05-29 11:34:22,265 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 2 for session test_task_1748489662
2025-05-29 11:34:22,266 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 3 for session test_task_1748489662
2025-05-29 11:34:22,267 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 4 for session test_task_1748489662
2025-05-29 11:34:22,268 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 5 for session test_task_1748489662
2025-05-29 11:34:22,268 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:246 - end_session - Ended conversation session: test_task_1748489662, status: ConversationStatus.SUCCESS
2025-05-29 11:34:22,287 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:34:22,288 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:34:22,653 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:34:22,654 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:34:22,654 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:34:22,655 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:34:22,655 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:34:22,800 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:34:22,801 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:34:22,801 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:34:22,802 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:34:22,803 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:34:23,166 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:34:23,166 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:34:23,166 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:34:23,168 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:34:23,168 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:34:23,371 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:34:23,371 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:34:23,372 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:45:43,865 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 11:45:43,866 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 11:45:43,866 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 11:45:43,867 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:45:43,867 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:45:44,061 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:45:44,062 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:45:44,063 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:45:44,164 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:45:44,165 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:45:44,573 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:45:44,574 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:45:44,575 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:45:44,578 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:97 - __init__ - ConversationLogger initialized, log_dir: E:\Projects\aider-plus\logs\conversations
2025-05-29 11:45:44,578 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:132 - start_session - Started conversation session: test_task_1748490344
2025-05-29 11:45:44,580 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 1 for session test_task_1748490344
2025-05-29 11:45:44,581 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 2 for session test_task_1748490344
2025-05-29 11:45:44,582 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 3 for session test_task_1748490344
2025-05-29 11:45:44,583 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 4 for session test_task_1748490344
2025-05-29 11:45:44,585 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 5 for session test_task_1748490344
2025-05-29 11:45:44,588 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:246 - end_session - Ended conversation session: test_task_1748490344, status: ConversationStatus.SUCCESS
2025-05-29 11:45:44,602 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:45:44,602 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:45:44,779 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:45:44,779 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:45:44,780 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:45:44,780 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:45:44,780 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:45:45,116 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:45:45,116 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:45:45,116 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:45:45,117 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:45:45,117 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:45:45,597 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:45:45,598 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:45:45,598 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:45:45,828 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:63 - find_git_executable - 找到Git可执行文件: git
2025-05-29 11:45:45,837 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:73 - __init__ - LLM重试处理器初始化完成
2025-05-29 11:45:45,839 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-29 11:45:45,839 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-29 11:45:45,840 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:45:45,841 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:45:46,027 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:45:46,028 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:45:46,029 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-29 11:45:46,029 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:45:46,030 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:45:46,239 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:45:46,239 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:45:46,240 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:45:46,240 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-29 11:45:46,241 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:45:46,241 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:45:46,370 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:45:46,370 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:45:46,371 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-29 11:45:46,372 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-29 11:45:46,372 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-29 11:45:46,372 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-29 11:45:46,373 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-29 11:45:46,373 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-29 11:45:46,373 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-29 11:45:46,373 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-29 11:45:46,374 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-29 11:45:46,374 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-29 11:45:46,374 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-29 11:45:46,374 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-29 11:45:46,374 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-29 11:45:46,374 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-29 11:45:46,375 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-29 11:45:46,375 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-29 11:45:46,375 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-29 11:45:46,375 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-29 11:45:46,376 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-29 11:45:46,376 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-29 11:45:46,376 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-29 11:47:33,939 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 11:47:33,939 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 11:47:33,939 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 11:47:33,941 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:47:33,941 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:47:34,148 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:47:34,148 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:47:34,149 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:47:34,251 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:47:34,251 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:47:34,455 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:47:34,456 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:47:34,456 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:47:34,460 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:97 - __init__ - ConversationLogger initialized, log_dir: E:\Projects\aider-plus\logs\conversations
2025-05-29 11:47:34,461 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:132 - start_session - Started conversation session: test_task_1748490454
2025-05-29 11:47:34,463 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 1 for session test_task_1748490454
2025-05-29 11:47:34,464 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 2 for session test_task_1748490454
2025-05-29 11:47:34,465 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 3 for session test_task_1748490454
2025-05-29 11:47:34,466 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 4 for session test_task_1748490454
2025-05-29 11:47:34,468 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 5 for session test_task_1748490454
2025-05-29 11:47:34,469 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:246 - end_session - Ended conversation session: test_task_1748490454, status: ConversationStatus.SUCCESS
2025-05-29 11:47:34,482 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:47:34,482 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:47:34,864 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:47:34,864 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:47:34,865 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:47:34,865 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:47:34,866 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:47:35,479 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:47:35,479 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:47:35,479 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:47:35,480 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:47:35,481 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:47:35,685 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:47:35,685 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:47:35,686 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:47:35,902 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:63 - find_git_executable - 找到Git可执行文件: git
2025-05-29 11:47:35,912 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:73 - __init__ - LLM重试处理器初始化完成
2025-05-29 11:47:35,914 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-29 11:47:35,914 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-29 11:47:35,915 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:47:35,916 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:47:37,038 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:47:37,038 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:47:37,039 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-29 11:47:37,039 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:47:37,039 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:47:37,162 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:47:37,163 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:47:37,164 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 11:47:37,164 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-29 11:47:37,165 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 11:47:37,165 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 11:47:37,302 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 11:47:37,302 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 11:47:37,303 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-29 11:47:37,304 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-29 11:47:37,304 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-29 11:47:37,305 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-29 11:47:37,305 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-29 11:47:37,305 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-29 11:47:37,305 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-29 11:47:37,305 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-29 11:47:37,305 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-29 11:47:37,306 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-29 11:47:37,306 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-29 11:47:37,306 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-29 11:47:37,306 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-29 11:47:37,306 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-29 11:47:37,306 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-29 11:47:37,307 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-29 11:47:37,307 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-29 11:47:37,308 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-29 11:47:37,308 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-29 11:47:37,308 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-29 11:47:37,309 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-29 12:11:02,851 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 12:11:02,852 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 12:11:02,852 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 12:11:02,853 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 12:11:02,854 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 12:11:03,045 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 12:11:03,046 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 12:11:03,048 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 12:11:03,149 - bot_agent - CRITICAL - logging_config.py:169 - handle_exception - 未捕获的异常
Traceback (most recent call last):
  File "<string>", line 3, in <module>
ModuleNotFoundError: No module named 'bot_agent.utils.tool_result'
2025-05-29 12:13:08,314 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 12:13:08,315 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 12:13:08,315 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 12:13:08,317 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 12:13:08,317 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 12:13:09,107 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 12:13:09,108 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 12:13:09,110 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 12:13:09,211 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 12:13:09,212 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 12:13:10,608 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 12:13:10,609 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 12:13:10,609 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 12:15:51,003 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 12:15:51,003 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 12:15:51,004 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 12:15:51,004 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-29 12:15:51,005 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-29 12:15:51,006 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 12:15:51,007 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 12:15:51,215 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 12:15:51,216 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 12:15:51,216 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-29 12:15:51,219 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 12:15:51,219 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 12:15:51,817 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 12:15:51,817 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 12:15:51,819 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 12:15:51,920 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-29 12:15:51,920 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 12:15:51,921 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 12:15:52,547 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 12:15:52,548 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 12:15:52,549 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-29 12:15:52,549 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-29 12:15:52,549 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-29 12:15:52,550 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-29 12:15:52,550 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-29 12:15:52,550 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-29 12:15:52,550 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-29 12:15:52,550 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-29 12:15:52,550 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-29 12:15:52,550 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-29 12:15:52,551 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-29 12:15:52,551 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-29 12:15:52,551 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-29 12:15:52,551 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-29 12:15:52,551 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-29 12:15:52,552 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-29 12:15:52,552 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-29 12:15:52,553 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-29 12:15:52,553 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-29 12:15:52,553 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-29 12:15:52,554 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-29 12:15:52,554 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 12:15:52,554 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 12:15:52,751 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 12:15:52,751 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 12:15:52,752 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 12:28:20,429 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 12:28:20,429 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 12:28:20,430 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 12:28:20,431 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-29 12:28:20,431 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-29 12:28:20,432 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 12:28:20,433 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 12:28:20,601 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 12:28:20,601 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 12:28:20,602 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-29 12:28:20,625 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 12:28:20,625 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 12:28:20,825 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 12:28:20,825 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 12:28:20,826 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 12:28:20,928 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-29 12:28:20,929 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 12:28:20,929 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 12:28:21,339 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 12:28:21,339 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 12:28:21,340 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-29 12:28:21,340 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-29 12:28:21,341 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-29 12:28:21,341 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-29 12:28:21,341 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-29 12:28:21,341 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-29 12:28:21,341 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-29 12:28:21,342 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-29 12:28:21,342 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-29 12:28:21,342 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-29 12:28:21,342 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-29 12:28:21,343 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-29 12:28:21,343 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-29 12:28:21,343 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-29 12:28:21,343 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-29 12:28:21,344 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-29 12:28:21,345 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-29 12:28:21,345 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-29 12:28:21,345 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-29 12:28:21,345 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-29 12:28:21,345 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-29 12:28:21,346 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 12:28:21,346 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 12:28:21,496 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 12:28:21,496 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 12:28:21,497 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 12:36:50,942 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 12:36:50,942 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 12:36:50,943 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 12:36:50,944 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-29 12:36:50,944 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-29 12:36:50,945 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 12:36:50,946 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 12:36:55,960 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: HTTPConnectionPool(host='***************', port=80): Max retries exceeded with url: /api/v4/version (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x0000013A0A227830>, 'Connection to *************** timed out. (connect timeout=5)'))
2025-05-29 12:36:55,961 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 12:36:55,961 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-29 12:36:55,990 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 12:36:55,990 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 12:37:01,004 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: HTTPConnectionPool(host='***************', port=80): Max retries exceeded with url: /api/v4/version (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x0000013A0A2276B0>, 'Connection to *************** timed out. (connect timeout=5)'))
2025-05-29 12:37:01,004 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 12:37:01,005 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 12:37:01,106 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:97 - __init__ - ConversationLogger initialized, log_dir: E:\Projects\aider-plus\logs\conversations
2025-05-29 12:37:01,107 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-29 12:37:01,108 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 12:37:01,108 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 12:37:01,764 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 12:37:01,765 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 12:37:01,765 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-29 12:37:01,766 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-29 12:37:01,766 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-29 12:37:01,767 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-29 12:37:01,767 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-29 12:37:01,767 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-29 12:37:01,767 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-29 12:37:01,768 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-29 12:37:01,768 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-29 12:37:01,768 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-29 12:37:01,768 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-29 12:37:01,769 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-29 12:37:01,769 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-29 12:37:01,769 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-29 12:37:01,769 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-29 12:37:01,770 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-29 12:37:01,770 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-29 12:37:01,770 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-29 12:37:01,770 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-29 12:37:01,771 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-29 12:37:01,771 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-29 12:37:01,771 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 12:37:01,772 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 12:37:02,377 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 12:37:02,378 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 12:37:02,378 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 12:37:02,379 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:132 - start_session - Started conversation session: test_round_numbering_1748493422
2025-05-29 12:37:02,380 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 2 for session test_round_numbering_1748493422
2025-05-29 12:37:02,382 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 4 for session test_round_numbering_1748493422
2025-05-29 12:37:02,383 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 6 for session test_round_numbering_1748493422
2025-05-29 12:37:02,384 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 8 for session test_round_numbering_1748493422
2025-05-29 12:37:02,386 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 10 for session test_round_numbering_1748493422
2025-05-29 12:37:02,389 - bot_agent.utils.conversation_logger - ERROR - conversation_logger.py:267 - _save_session_to_file - Failed to save session test_round_numbering_1748493422: 'str' object has no attribute 'value'
2025-05-29 12:37:02,390 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:246 - end_session - Ended conversation session: test_round_numbering_1748493422, status: success
2025-05-29 13:23:54,463 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 13:23:54,463 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 13:23:54,463 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 13:23:54,465 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 13:23:54,465 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 13:23:54,654 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 13:23:54,654 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 13:23:54,655 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 13:23:54,755 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 13:23:54,756 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 13:23:55,156 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 13:23:55,157 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 13:23:55,158 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 13:23:55,399 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:63 - find_git_executable - 找到Git可执行文件: git
2025-05-29 13:23:55,408 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:73 - __init__ - LLM重试处理器初始化完成
2025-05-29 13:23:55,411 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:97 - __init__ - ConversationLogger initialized, log_dir: E:\Projects\aider-plus\logs\conversations
2025-05-29 13:23:55,413 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-29 13:23:55,414 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-29 13:23:55,415 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 13:23:55,416 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 13:23:55,617 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 13:23:55,617 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 13:23:55,618 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-29 13:23:55,619 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-29 13:23:55,619 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 13:23:55,620 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 13:23:55,770 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 13:23:55,771 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 13:23:55,771 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-29 13:23:55,771 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-29 13:23:55,772 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-29 13:23:55,772 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-29 13:23:55,773 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-29 13:23:55,773 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-29 13:23:55,773 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-29 13:23:55,773 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-29 13:23:55,773 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-29 13:23:55,773 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-29 13:23:55,774 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-29 13:23:55,774 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-29 13:23:55,774 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-29 13:23:55,775 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-29 13:23:55,775 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-29 13:23:55,775 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-29 13:23:55,775 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-29 13:23:55,776 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-29 13:23:55,777 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-29 13:23:55,777 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-29 13:23:55,777 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-29 13:23:55,777 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:132 - start_session - Started conversation session: round_number_test_1748496235
2025-05-29 13:23:55,779 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 1 for session round_number_test_1748496235
2025-05-29 13:23:55,781 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 2 for session round_number_test_1748496235
2025-05-29 13:26:22,433 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 13:26:22,433 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 13:26:22,434 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 13:26:22,435 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 13:26:22,435 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 13:26:22,825 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 13:26:22,826 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 13:26:22,827 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 13:26:23,128 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:63 - find_git_executable - 找到Git可执行文件: git
2025-05-29 13:26:23,137 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:73 - __init__ - LLM重试处理器初始化完成
2025-05-29 13:26:23,141 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:97 - __init__ - ConversationLogger initialized, log_dir: E:\Projects\aider-plus\logs\conversations
2025-05-29 13:26:23,143 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-29 13:26:23,143 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-29 13:26:23,145 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 13:26:23,145 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 13:26:23,331 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 13:26:23,331 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 13:26:23,332 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-29 13:26:23,332 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-29 13:26:23,333 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 13:26:23,333 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 13:26:23,441 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 13:26:23,442 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 13:26:23,442 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:108 - __init__ - 使用默认的Git仓库目录: E:\Projects\git-repos
2025-05-29 13:26:23,443 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\Projects\git-repos
2025-05-29 13:26:23,443 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-29 13:26:23,443 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: information_query
2025-05-29 13:26:23,444 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TerminalTools
2025-05-29 13:26:23,444 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: TestingTools
2025-05-29 13:26:23,444 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: LogAnalysisTools
2025-05-29 13:26:23,444 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DatabaseTools
2025-05-29 13:26:23,444 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DependencyTools
2025-05-29 13:26:23,445 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DebugTools
2025-05-29 13:26:23,445 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: FrontendDebugTools
2025-05-29 13:26:23,445 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: RefactorTools
2025-05-29 13:26:23,446 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: DocumentationTools
2025-05-29 13:26:23,446 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: SecurityTools
2025-05-29 13:26:23,446 - bot_agent.tools.base_tool - INFO - base_tool.py:127 - register - 注册工具: CodeGenerationTools
2025-05-29 13:26:23,446 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-29 13:26:23,447 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-29 13:26:23,447 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-29 13:26:23,448 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-29 13:26:23,448 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\Projects\git-repos
2025-05-29 13:26:23,448 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-29 13:26:23,449 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:132 - start_session - Started conversation session: round_number_test_1748496383
2025-05-29 13:26:23,454 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 1 for session round_number_test_1748496383
2025-05-29 13:26:23,455 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:210 - log_round - Logged round 2 for session round_number_test_1748496383
2025-05-29 13:26:23,456 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:263 - _ai_continuous_inquiry_fix - 🧠 启动AI持续追问修复机制...
2025-05-29 13:26:23,608 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:291 - _ai_continuous_inquiry_fix - ✅ 使用现有会话ID继续AI追问: round_number_test_1748496383
2025-05-29 13:26:23,608 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:294 - _ai_continuous_inquiry_fix - 🔍 AI深度追问（追问轮次 1/5）...
2025-05-29 13:26:23,608 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:348 - _ai_continuous_inquiry_fix - OPENROUTER_API_KEY未设置，无法进行AI追问
2025-05-29 13:26:23,610 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:246 - end_session - Ended conversation session: round_number_test_1748496383, status: success
2025-05-29 14:52:38,278 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 14:52:38,279 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 14:52:38,279 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 14:52:38,280 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 14:52:38,281 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 14:52:38,924 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 14:52:38,924 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 14:52:38,925 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 14:52:39,027 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 14:52:39,027 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 14:52:39,140 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 14:52:39,140 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 14:52:39,140 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 15:06:25,692 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 15:06:25,693 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 15:06:25,693 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 15:06:25,694 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 15:06:25,695 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 15:06:30,710 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: HTTPConnectionPool(host='***************', port=80): Max retries exceeded with url: /api/v4/version (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x0000017C23295970>, 'Connection to *************** timed out. (connect timeout=5)'))
2025-05-29 15:06:30,711 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 15:06:30,712 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 15:06:30,816 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:517 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 15:06:30,817 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:522 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 15:06:30,818 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\aider-git-repos\ai-proxy
2025-05-29 15:06:34,581 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 15:06:34,581 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 15:06:37,177 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 15:06:37,177 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 15:06:37,177 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 15:06:37,179 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:73 - __init__ - LLM重试处理器初始化完成
2025-05-29 15:06:37,331 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 1): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:06:37,331 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 1.2 秒后重试...
2025-05-29 15:06:38,585 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 2): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:06:38,586 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 2.5 秒后重试...
2025-05-29 15:06:41,083 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 3): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:06:41,084 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 4.4 秒后重试...
2025-05-29 15:06:45,527 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:160 - execute_with_retry - 达到最大重试次数，使用降级策略
2025-05-29 15:06:45,528 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:717 - _ai_generate_fix_plan - AI响应JSON解析失败: Expecting value: line 2 column 1 (char 1)
2025-05-29 15:06:45,528 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:724 - _ai_generate_fix_plan - 文本解析也失败，使用智能fallback策略
2025-05-29 15:06:45,528 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:728 - _ai_generate_fix_plan - AI修复方案生成异常: 'IntelligentToolCoordinator' object has no attribute '_create_intelligent_fallback_plan'
2025-05-29 15:06:45,529 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:730 - _ai_generate_fix_plan - AI修复方案生成异常，使用智能fallback策略
2025-05-29 15:06:45,530 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:2133 - _ai_generate_fix_plan - 同步AI修复方案生成失败: 'IntelligentToolCoordinator' object has no attribute '_create_intelligent_fallback_plan'
2025-05-29 15:06:45,531 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:126 - analyze_job_errors - 🔍 开始智能错误分析...
2025-05-29 15:06:45,531 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:487 - analyze_job_log - 🔍 开始分析作业日志...
2025-05-29 15:06:45,533 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:214 - analyze_logs - 开始分析日志文件: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp0synty59.log']
2025-05-29 15:06:45,553 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:502 - analyze_job_log - ✅ 作业日志分析完成
2025-05-29 15:10:43,296 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 15:10:43,297 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 15:10:43,297 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 15:10:43,299 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 15:10:43,299 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 15:10:43,640 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 15:10:43,640 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 15:10:43,642 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 15:10:43,745 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:517 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 15:10:43,747 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:522 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 15:10:43,747 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\aider-git-repos\ai-proxy
2025-05-29 15:10:47,626 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 15:10:47,626 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 15:10:50,217 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 15:10:50,217 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 15:10:50,217 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 15:10:50,220 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:73 - __init__ - LLM重试处理器初始化完成
2025-05-29 15:10:50,380 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 1): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:10:50,380 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 1.3 秒后重试...
2025-05-29 15:10:51,649 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 2): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:10:51,650 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 2.4 秒后重试...
2025-05-29 15:10:54,088 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 3): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:10:54,089 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 5.0 秒后重试...
2025-05-29 15:10:59,105 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:160 - execute_with_retry - 达到最大重试次数，使用降级策略
2025-05-29 15:10:59,106 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:717 - _ai_generate_fix_plan - AI响应JSON解析失败: Expecting value: line 2 column 1 (char 1)
2025-05-29 15:10:59,106 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:724 - _ai_generate_fix_plan - 文本解析也失败，使用智能fallback策略
2025-05-29 15:10:59,107 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:739 - _create_intelligent_fallback_plan - 🧠 创建智能fallback修复计划...
2025-05-29 15:10:59,107 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:126 - analyze_job_errors - 🔍 开始智能错误分析...
2025-05-29 15:10:59,108 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:487 - analyze_job_log - 🔍 开始分析作业日志...
2025-05-29 15:10:59,110 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:214 - analyze_logs - 开始分析日志文件: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpi302fgz9.log']
2025-05-29 15:10:59,114 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:502 - analyze_job_log - ✅ 作业日志分析完成
2025-05-29 15:12:22,277 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 15:12:22,278 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 15:12:22,278 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 15:12:22,279 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 15:12:22,279 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 15:12:22,632 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 15:12:22,632 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 15:12:22,633 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 15:12:22,737 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:517 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 15:12:22,738 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:522 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 15:12:22,738 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\aider-git-repos\ai-proxy
2025-05-29 15:12:26,528 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 15:12:26,528 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 15:12:29,272 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 15:12:29,273 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 15:12:29,273 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 15:12:29,276 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:73 - __init__ - LLM重试处理器初始化完成
2025-05-29 15:12:29,445 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 1): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:12:29,446 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 1.3 秒后重试...
2025-05-29 15:12:30,749 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 2): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:12:30,749 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 2.4 秒后重试...
2025-05-29 15:12:33,187 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 3): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:12:33,188 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 5.1 秒后重试...
2025-05-29 15:12:38,312 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:160 - execute_with_retry - 达到最大重试次数，使用降级策略
2025-05-29 15:12:38,313 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:717 - _ai_generate_fix_plan - AI响应JSON解析失败: Expecting value: line 2 column 1 (char 1)
2025-05-29 15:12:38,313 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:724 - _ai_generate_fix_plan - 文本解析也失败，使用智能fallback策略
2025-05-29 15:12:38,313 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:739 - _create_intelligent_fallback_plan - 🧠 创建智能fallback修复计划...
2025-05-29 15:12:38,315 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:126 - analyze_job_errors - 🔍 开始智能错误分析...
2025-05-29 15:12:38,315 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:487 - analyze_job_log - 🔍 开始分析作业日志...
2025-05-29 15:12:38,317 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:214 - analyze_logs - 开始分析日志文件: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpor_khmw8.log']
2025-05-29 15:12:38,321 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:502 - analyze_job_log - ✅ 作业日志分析完成
2025-05-29 15:16:28,511 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 15:16:28,512 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 15:16:28,512 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 15:16:28,513 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 15:16:28,513 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 15:16:28,784 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 15:16:28,785 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 15:16:28,786 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 15:16:28,890 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:517 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 15:16:28,891 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:522 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 15:16:28,892 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\aider-git-repos\ai-proxy
2025-05-29 15:16:32,890 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 15:16:32,890 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 15:16:35,412 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 15:16:35,412 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 15:16:35,412 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 15:16:35,415 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:73 - __init__ - LLM重试处理器初始化完成
2025-05-29 15:16:35,570 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 1): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:16:35,571 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 1.2 秒后重试...
2025-05-29 15:16:36,808 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 2): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:16:36,811 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 2.3 秒后重试...
2025-05-29 15:16:39,174 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 3): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:16:39,174 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 4.8 秒后重试...
2025-05-29 15:16:43,942 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:160 - execute_with_retry - 达到最大重试次数，使用降级策略
2025-05-29 15:16:43,942 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:717 - _ai_generate_fix_plan - AI响应JSON解析失败: Expecting value: line 2 column 1 (char 1)
2025-05-29 15:16:43,943 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:724 - _ai_generate_fix_plan - 文本解析也失败，使用智能fallback策略
2025-05-29 15:16:43,943 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:739 - _create_intelligent_fallback_plan_async - 🧠 创建智能fallback修复计划...
2025-05-29 15:16:43,944 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:126 - analyze_job_errors - 🔍 开始智能错误分析...
2025-05-29 15:16:43,944 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:487 - analyze_job_log - 🔍 开始分析作业日志...
2025-05-29 15:16:43,946 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:214 - analyze_logs - 开始分析日志文件: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpx4j81fr_.log']
2025-05-29 15:16:43,950 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:502 - analyze_job_log - ✅ 作业日志分析完成
2025-05-29 15:18:30,977 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 15:18:30,977 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 15:18:30,978 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 15:18:30,979 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 15:18:30,979 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 15:18:31,395 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 15:18:31,395 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 15:18:31,397 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 15:18:31,498 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:517 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 15:18:31,500 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:522 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 15:18:31,501 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\aider-git-repos\ai-proxy
2025-05-29 15:18:35,299 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 15:18:35,300 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 15:18:37,893 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 15:18:37,893 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 15:18:37,893 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 15:18:37,895 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:73 - __init__ - LLM重试处理器初始化完成
2025-05-29 15:18:38,059 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 1): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:18:38,059 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 1.2 秒后重试...
2025-05-29 15:18:39,324 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 2): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:18:39,324 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 2.3 秒后重试...
2025-05-29 15:18:41,670 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 3): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:18:41,671 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 4.5 秒后重试...
2025-05-29 15:18:46,229 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:160 - execute_with_retry - 达到最大重试次数，使用降级策略
2025-05-29 15:18:46,230 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:717 - _ai_generate_fix_plan - AI响应JSON解析失败: Expecting value: line 2 column 1 (char 1)
2025-05-29 15:18:46,230 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:724 - _ai_generate_fix_plan - 文本解析也失败，使用智能fallback策略
2025-05-29 15:18:46,230 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:739 - _create_intelligent_fallback_plan_async - 🧠 创建智能fallback修复计划...
2025-05-29 15:21:40,862 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 15:21:40,862 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 15:21:40,862 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 15:21:40,864 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 15:21:40,864 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 15:21:41,050 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 15:21:41,050 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 15:21:41,051 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 15:21:41,153 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:517 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 15:21:41,156 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:522 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 15:21:41,156 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\aider-git-repos\ai-proxy
2025-05-29 15:21:44,841 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 15:21:44,842 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 15:21:47,377 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 15:21:47,377 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 15:21:47,377 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 15:21:47,380 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:73 - __init__ - LLM重试处理器初始化完成
2025-05-29 15:21:47,527 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 1): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:21:47,527 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 1.1 秒后重试...
2025-05-29 15:21:48,645 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 2): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:21:48,646 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 2.4 秒后重试...
2025-05-29 15:21:51,021 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 3): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:21:51,022 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 4.9 秒后重试...
2025-05-29 15:21:55,959 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:160 - execute_with_retry - 达到最大重试次数，使用降级策略
2025-05-29 15:21:55,960 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:717 - _ai_generate_fix_plan - AI响应JSON解析失败: Expecting value: line 2 column 1 (char 1)
2025-05-29 15:21:55,960 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:724 - _ai_generate_fix_plan - 文本解析也失败，使用智能fallback策略
2025-05-29 15:21:55,961 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:739 - _create_intelligent_fallback_plan_async - 🧠 创建智能fallback修复计划...
2025-05-29 15:23:27,985 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 15:23:27,985 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 15:23:27,985 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 15:23:27,986 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 15:23:27,987 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 15:23:28,198 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 15:23:28,198 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 15:23:28,200 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 15:23:28,303 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:517 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 15:23:28,305 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:522 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 15:23:28,305 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\aider-git-repos\ai-proxy
2025-05-29 15:23:32,176 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 15:23:32,176 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 15:23:34,694 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 15:23:34,694 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 15:23:34,694 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 15:23:34,697 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:73 - __init__ - LLM重试处理器初始化完成
2025-05-29 15:23:34,848 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 1): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:23:34,849 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 1.2 秒后重试...
2025-05-29 15:23:36,076 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 2): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:23:36,076 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 2.3 秒后重试...
2025-05-29 15:23:38,377 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 3): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:23:38,378 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 4.5 秒后重试...
2025-05-29 15:23:42,837 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:160 - execute_with_retry - 达到最大重试次数，使用降级策略
2025-05-29 15:23:42,838 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:717 - _ai_generate_fix_plan - AI响应JSON解析失败: Expecting value: line 2 column 1 (char 1)
2025-05-29 15:23:42,838 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:724 - _ai_generate_fix_plan - 文本解析也失败，使用智能fallback策略
2025-05-29 15:23:42,838 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:739 - _create_intelligent_fallback_plan_async - 🧠 创建智能fallback修复计划...
2025-05-29 15:23:42,839 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:263 - _ai_continuous_inquiry_fix - 🧠 启动AI持续追问修复机制...
2025-05-29 15:23:42,843 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:97 - __init__ - ConversationLogger initialized, log_dir: E:\Projects\aider-plus\logs\conversations
2025-05-29 15:23:42,844 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:283 - _ai_continuous_inquiry_fix - 未找到会话ID，创建临时会话: ai_inquiry_1748503422
2025-05-29 15:23:42,844 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:132 - start_session - Started conversation session: ai_inquiry_1748503422_1748503422
2025-05-29 15:23:42,844 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:294 - _ai_continuous_inquiry_fix - 🔍 AI深度追问（追问轮次 1/5）...
2025-05-29 15:23:42,845 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:348 - _ai_continuous_inquiry_fix - OPENROUTER_API_KEY未设置，无法进行AI追问
2025-05-29 15:23:42,845 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:126 - analyze_job_errors - 🔍 开始智能错误分析...
2025-05-29 15:23:42,846 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:487 - analyze_job_log - 🔍 开始分析作业日志...
2025-05-29 15:23:42,848 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:214 - analyze_logs - 开始分析日志文件: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpmebtds6q.log']
2025-05-29 15:23:42,852 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:502 - analyze_job_log - ✅ 作业日志分析完成
2025-05-29 15:23:42,853 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:517 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 15:23:42,854 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:522 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 15:23:42,854 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\aider-git-repos\ai-proxy
2025-05-29 15:23:46,524 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 15:23:46,525 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 15:23:49,144 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 15:23:49,144 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 15:23:49,144 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 15:23:49,145 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 1): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:23:49,145 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 1.3 秒后重试...
2025-05-29 15:23:50,435 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 2): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:23:50,435 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 2.3 秒后重试...
2025-05-29 15:23:52,704 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 3): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:23:52,704 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 4.9 秒后重试...
2025-05-29 15:23:57,659 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:160 - execute_with_retry - 达到最大重试次数，使用降级策略
2025-05-29 15:23:57,660 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:717 - _ai_generate_fix_plan - AI响应JSON解析失败: Expecting value: line 2 column 1 (char 1)
2025-05-29 15:23:57,660 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:724 - _ai_generate_fix_plan - 文本解析也失败，使用智能fallback策略
2025-05-29 15:23:57,661 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:739 - _create_intelligent_fallback_plan_async - 🧠 创建智能fallback修复计划...
2025-05-29 15:34:33,229 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 15:34:33,230 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 15:34:33,230 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 15:34:33,231 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 15:34:33,232 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 15:34:34,108 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 15:34:34,108 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 15:34:34,110 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 15:34:34,211 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:517 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 15:34:34,212 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:522 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 15:34:34,213 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\aider-git-repos\ai-proxy
2025-05-29 15:34:38,136 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 15:34:38,136 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 15:34:40,675 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 15:34:40,675 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 15:34:40,676 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 15:34:40,678 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:73 - __init__ - LLM重试处理器初始化完成
2025-05-29 15:34:40,827 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 1): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:34:40,827 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 1.1 秒后重试...
2025-05-29 15:34:41,971 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 2): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:34:41,972 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 2.3 秒后重试...
2025-05-29 15:34:44,318 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 3): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:34:44,319 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 5.0 秒后重试...
2025-05-29 15:34:49,377 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:160 - execute_with_retry - 达到最大重试次数，使用降级策略
2025-05-29 15:34:49,378 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:717 - _ai_generate_fix_plan - AI响应JSON解析失败: Expecting value: line 2 column 1 (char 1)
2025-05-29 15:34:49,379 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:724 - _ai_generate_fix_plan - 文本解析也失败，使用智能fallback策略
2025-05-29 15:34:49,379 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:739 - _create_intelligent_fallback_plan_async - 🧠 创建智能fallback修复计划...
2025-05-29 15:34:49,380 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:517 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 15:34:49,380 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:522 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 15:34:49,380 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\aider-git-repos\ai-proxy
2025-05-29 15:34:53,087 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 15:34:53,088 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 15:34:55,570 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 15:34:55,571 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 15:34:55,571 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 15:34:55,571 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 1): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:34:55,571 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 1.3 秒后重试...
2025-05-29 15:34:56,859 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 2): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:34:56,860 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 2.4 秒后重试...
2025-05-29 15:34:59,223 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 3): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:34:59,223 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 4.8 秒后重试...
2025-05-29 15:35:04,016 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:160 - execute_with_retry - 达到最大重试次数，使用降级策略
2025-05-29 15:35:04,017 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:717 - _ai_generate_fix_plan - AI响应JSON解析失败: Expecting value: line 2 column 1 (char 1)
2025-05-29 15:35:04,017 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:724 - _ai_generate_fix_plan - 文本解析也失败，使用智能fallback策略
2025-05-29 15:35:04,017 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:739 - _create_intelligent_fallback_plan_async - 🧠 创建智能fallback修复计划...
2025-05-29 15:35:04,018 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1179 - _execute_ai_fix_step - 🔧 执行AI修复步骤: 修复flake8配置文件
2025-05-29 15:35:04,018 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1180 - _execute_ai_fix_step - 📝 执行命令: python -c "import os; f=os.path.join('E:\aider-git-repos\ai-proxy', '.flake8'); content=open(f).read() if os.path.exists(f) else '[flake8]\nextend-ignore = E203,W503'; open(f, 'w').write(content.replace('Ignore,', '').replace(',Ignore', '').replace('Ignore', ''))"
2025-05-29 15:35:04,018 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1211 - _execute_command_with_retry - 🔄 第 1 次尝试执行: python -c "import os; f=os.path.join('E:\aider-git-repos\ai-proxy', '.flake8'); content=open(f).read() if os.path.exists(f) else '[flake8]\nextend-ignore = E203,W503'; open(f, 'w').write(content.replace('Ignore,', '').replace(',Ignore', '').replace('Ignore', ''))"
2025-05-29 15:35:06,609 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "python -c "import os; f=os.path.join('E:\aider-git-repos\ai-proxy', '.flake8'); content=open(f).read() if os.path.exists(f) else '[flake8]\nextend-ignore = E203,W503'; open(f, 'w').write(content.replace('Ignore,', '').replace(',Ignore', '').replace('Ignore', ''))""
2025-05-29 15:35:06,609 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: 所在位置 行:1 字符: 174
+ ... exists(f) else '[flake8]\nextend-ignore = E203,W503'; open(f, 'w').wr ...
+                                                                 ~
参数列表中缺少参量。
所在位置 行:1 字符: 187
+ ... se '[flake8]\nextend-ignore = E203,W503'; open(f, 'w').write(content. ...
+                                                                  ~
方法调用中缺少“)”。
所在位置 行:1 字符: 262
+ ... .replace('Ignore,', '').replace(',Ignore', '').replace('Ignore', ''))
+                                                                         ~
表达式或语句中包含意外的标记“)”。
    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException
    + FullyQualifiedErrorId : MissingArgument
 

2025-05-29 15:35:06,610 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:1244 - _execute_command_with_retry - ❌ 第 1 次尝试失败: 所在位置 行:1 字符: 174
+ ... exists(f) else '[flake8]\nextend-ignore = E203,W503'; open(f, 'w').wr ...
+                                                                 ~
参数列表中缺少参量。
所在位置 行:1 字符: 187
+ ... se '[flake8]\nextend-ignore = E203,W503'; open(f, 'w').write(content. ...
+                                                                  ~
方法调用中缺少“)”。
所在位置 行:1 字符: 262
+ ... .replace('Ignore,', '').replace(',Ignore', '').replace('Ignore', ''))
+                                                                         ~
表达式或语句中包含意外的标记“)”。
    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException
    + FullyQualifiedErrorId : MissingArgument
 

2025-05-29 15:35:06,610 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1283 - _get_ai_alternative_command - 🔄 生成替代命令...
2025-05-29 15:35:06,610 - bot_agent.tools.intelligent_tool_coordinator - DEBUG - intelligent_tool_coordinator.py:1365 - _try_ai_alternative - OPENROUTER_API_KEY未设置，跳过AI生成
2025-05-29 15:35:06,610 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:1297 - _get_ai_alternative_command - ⚠️ 无法生成替代命令
2025-05-29 15:35:06,610 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:1259 - _execute_command_with_retry - ⚠️ AI无法提供替代命令，继续使用原命令
2025-05-29 15:35:06,610 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1211 - _execute_command_with_retry - 🔄 第 2 次尝试执行: python -c "import os; f=os.path.join('E:\aider-git-repos\ai-proxy', '.flake8'); content=open(f).read() if os.path.exists(f) else '[flake8]\nextend-ignore = E203,W503'; open(f, 'w').write(content.replace('Ignore,', '').replace(',Ignore', '').replace('Ignore', ''))"
2025-05-29 15:35:09,186 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "python -c "import os; f=os.path.join('E:\aider-git-repos\ai-proxy', '.flake8'); content=open(f).read() if os.path.exists(f) else '[flake8]\nextend-ignore = E203,W503'; open(f, 'w').write(content.replace('Ignore,', '').replace(',Ignore', '').replace('Ignore', ''))""
2025-05-29 15:35:09,186 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: 所在位置 行:1 字符: 174
+ ... exists(f) else '[flake8]\nextend-ignore = E203,W503'; open(f, 'w').wr ...
+                                                                 ~
参数列表中缺少参量。
所在位置 行:1 字符: 187
+ ... se '[flake8]\nextend-ignore = E203,W503'; open(f, 'w').write(content. ...
+                                                                  ~
方法调用中缺少“)”。
所在位置 行:1 字符: 262
+ ... .replace('Ignore,', '').replace(',Ignore', '').replace('Ignore', ''))
+                                                                         ~
表达式或语句中包含意外的标记“)”。
    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException
    + FullyQualifiedErrorId : MissingArgument
 

2025-05-29 15:35:09,187 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:1244 - _execute_command_with_retry - ❌ 第 2 次尝试失败: 所在位置 行:1 字符: 174
+ ... exists(f) else '[flake8]\nextend-ignore = E203,W503'; open(f, 'w').wr ...
+                                                                 ~
参数列表中缺少参量。
所在位置 行:1 字符: 187
+ ... se '[flake8]\nextend-ignore = E203,W503'; open(f, 'w').write(content. ...
+                                                                  ~
方法调用中缺少“)”。
所在位置 行:1 字符: 262
+ ... .replace('Ignore,', '').replace(',Ignore', '').replace('Ignore', ''))
+                                                                         ~
表达式或语句中包含意外的标记“)”。
    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException
    + FullyQualifiedErrorId : MissingArgument
 

2025-05-29 15:35:09,187 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1283 - _get_ai_alternative_command - 🔄 生成替代命令...
2025-05-29 15:35:09,188 - bot_agent.tools.intelligent_tool_coordinator - DEBUG - intelligent_tool_coordinator.py:1365 - _try_ai_alternative - OPENROUTER_API_KEY未设置，跳过AI生成
2025-05-29 15:35:09,188 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:1297 - _get_ai_alternative_command - ⚠️ 无法生成替代命令
2025-05-29 15:35:09,188 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:1259 - _execute_command_with_retry - ⚠️ AI无法提供替代命令，继续使用原命令
2025-05-29 15:35:09,188 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1211 - _execute_command_with_retry - 🔄 第 3 次尝试执行: python -c "import os; f=os.path.join('E:\aider-git-repos\ai-proxy', '.flake8'); content=open(f).read() if os.path.exists(f) else '[flake8]\nextend-ignore = E203,W503'; open(f, 'w').write(content.replace('Ignore,', '').replace(',Ignore', '').replace('Ignore', ''))"
2025-05-29 15:35:11,703 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "python -c "import os; f=os.path.join('E:\aider-git-repos\ai-proxy', '.flake8'); content=open(f).read() if os.path.exists(f) else '[flake8]\nextend-ignore = E203,W503'; open(f, 'w').write(content.replace('Ignore,', '').replace(',Ignore', '').replace('Ignore', ''))""
2025-05-29 15:35:11,703 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: 所在位置 行:1 字符: 174
+ ... exists(f) else '[flake8]\nextend-ignore = E203,W503'; open(f, 'w').wr ...
+                                                                 ~
参数列表中缺少参量。
所在位置 行:1 字符: 187
+ ... se '[flake8]\nextend-ignore = E203,W503'; open(f, 'w').write(content. ...
+                                                                  ~
方法调用中缺少“)”。
所在位置 行:1 字符: 262
+ ... .replace('Ignore,', '').replace(',Ignore', '').replace('Ignore', ''))
+                                                                         ~
表达式或语句中包含意外的标记“)”。
    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException
    + FullyQualifiedErrorId : MissingArgument
 

2025-05-29 15:35:11,704 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:1244 - _execute_command_with_retry - ❌ 第 3 次尝试失败: 所在位置 行:1 字符: 174
+ ... exists(f) else '[flake8]\nextend-ignore = E203,W503'; open(f, 'w').wr ...
+                                                                 ~
参数列表中缺少参量。
所在位置 行:1 字符: 187
+ ... se '[flake8]\nextend-ignore = E203,W503'; open(f, 'w').write(content. ...
+                                                                  ~
方法调用中缺少“)”。
所在位置 行:1 字符: 262
+ ... .replace('Ignore,', '').replace(',Ignore', '').replace('Ignore', ''))
+                                                                         ~
表达式或语句中包含意外的标记“)”。
    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException
    + FullyQualifiedErrorId : MissingArgument
 

2025-05-29 15:47:22,923 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 15:47:22,923 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 15:47:22,923 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 15:47:22,924 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 15:47:22,925 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 15:47:23,144 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 15:47:23,144 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 15:47:23,146 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 15:47:23,248 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:126 - analyze_job_errors - 🔍 开始智能错误分析...
2025-05-29 15:47:23,248 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:487 - analyze_job_log - 🔍 开始分析作业日志...
2025-05-29 15:47:23,250 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:214 - analyze_logs - 开始分析日志文件: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpunl_xh2p.log']
2025-05-29 15:47:23,269 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:502 - analyze_job_log - ✅ 作业日志分析完成
2025-05-29 15:47:23,271 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:517 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 15:47:23,273 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:522 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 15:47:23,273 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\aider-git-repos\ai-proxy
2025-05-29 15:47:27,056 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 15:47:27,056 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 15:47:29,625 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 15:47:29,626 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 15:47:29,626 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 15:47:29,629 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:73 - __init__ - LLM重试处理器初始化完成
2025-05-29 15:47:29,778 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 1): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:47:29,779 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 1.1 秒后重试...
2025-05-29 15:47:30,939 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 2): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:47:30,940 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 2.4 秒后重试...
2025-05-29 15:47:33,331 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 3): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:47:33,332 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 5.0 秒后重试...
2025-05-29 15:47:38,296 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:160 - execute_with_retry - 达到最大重试次数，使用降级策略
2025-05-29 15:47:38,297 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:717 - _ai_generate_fix_plan - AI响应JSON解析失败: Expecting value: line 2 column 1 (char 1)
2025-05-29 15:47:38,297 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:724 - _ai_generate_fix_plan - 文本解析也失败，使用智能fallback策略
2025-05-29 15:47:38,297 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:739 - _create_intelligent_fallback_plan_async - 🧠 创建智能fallback修复计划...
2025-05-29 15:47:38,298 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1179 - _execute_ai_fix_step - 🔧 执行AI修复步骤: 基本项目诊断
2025-05-29 15:47:38,298 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1180 - _execute_ai_fix_step - 📝 执行命令: echo '开始诊断项目: E:\aider-git-repos\ai-proxy'
2025-05-29 15:47:38,298 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1211 - _execute_command_with_retry - 🔄 第 1 次尝试执行: echo '开始诊断项目: E:\aider-git-repos\ai-proxy'
2025-05-29 15:47:40,801 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "echo '开始诊断项目: E:\aider-git-repos\ai-proxy'"
2025-05-29 15:47:40,802 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 开始诊断项目: E:\aider-git-repos\ai-proxy
...
2025-05-29 15:47:40,802 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1232 - _execute_command_with_retry - ✅ 第 1 次尝试成功
2025-05-29 15:59:21,554 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 15:59:21,555 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 15:59:21,555 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 15:59:21,556 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 15:59:21,557 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 15:59:21,953 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 15:59:21,954 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 15:59:21,955 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 15:59:22,056 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:126 - analyze_job_errors - 🔍 开始智能错误分析...
2025-05-29 15:59:22,057 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:487 - analyze_job_log - 🔍 开始分析作业日志...
2025-05-29 15:59:22,059 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:214 - analyze_logs - 开始分析日志文件: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp873mzvn6.log']
2025-05-29 15:59:22,076 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:502 - analyze_job_log - ✅ 作业日志分析完成
2025-05-29 15:59:22,077 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:126 - analyze_job_errors - 🔍 开始智能错误分析...
2025-05-29 15:59:22,078 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:487 - analyze_job_log - 🔍 开始分析作业日志...
2025-05-29 15:59:22,079 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:214 - analyze_logs - 开始分析日志文件: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpri6g7bi9.log']
2025-05-29 15:59:22,088 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:502 - analyze_job_log - ✅ 作业日志分析完成
2025-05-29 15:59:22,090 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:517 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 15:59:22,092 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:522 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 15:59:22,092 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\aider-git-repos\ai-proxy
2025-05-29 15:59:25,956 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 15:59:25,956 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 15:59:28,524 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 15:59:28,524 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 15:59:28,525 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 15:59:28,527 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:73 - __init__ - LLM重试处理器初始化完成
2025-05-29 15:59:28,675 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 1): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:59:28,675 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 1.2 秒后重试...
2025-05-29 15:59:29,863 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 2): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:59:29,863 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 2.6 秒后重试...
2025-05-29 15:59:32,442 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 3): OPENROUTER_API_KEY环境变量未设置
2025-05-29 15:59:32,442 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 4.5 秒后重试...
2025-05-29 15:59:36,899 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:160 - execute_with_retry - 达到最大重试次数，使用降级策略
2025-05-29 15:59:36,899 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:717 - _ai_generate_fix_plan - AI响应JSON解析失败: Expecting value: line 2 column 1 (char 1)
2025-05-29 15:59:36,900 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:724 - _ai_generate_fix_plan - 文本解析也失败，使用智能fallback策略
2025-05-29 15:59:36,900 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:739 - _create_intelligent_fallback_plan_async - 🧠 创建智能fallback修复计划...
2025-05-29 16:08:32,307 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 16:08:32,308 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 16:08:32,308 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 16:08:32,309 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 16:08:32,310 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 16:08:32,785 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 16:08:32,785 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 16:08:32,787 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 16:08:32,891 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:519 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 16:08:32,894 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:524 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 16:08:32,894 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\aider-git-repos\ai-proxy
2025-05-29 16:08:36,681 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 16:08:36,681 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 16:08:39,192 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 16:08:39,192 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 16:08:39,192 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 16:08:39,196 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:73 - __init__ - LLM重试处理器初始化完成
2025-05-29 16:08:39,348 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 1): OPENROUTER_API_KEY环境变量未设置
2025-05-29 16:08:39,348 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 1.2 秒后重试...
2025-05-29 16:08:40,548 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 2): OPENROUTER_API_KEY环境变量未设置
2025-05-29 16:08:40,548 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 2.4 秒后重试...
2025-05-29 16:08:42,970 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 3): OPENROUTER_API_KEY环境变量未设置
2025-05-29 16:08:42,971 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 4.9 秒后重试...
2025-05-29 16:08:47,893 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:160 - execute_with_retry - 达到最大重试次数，使用降级策略
2025-05-29 16:08:47,894 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:741 - _ai_generate_fix_plan - AI响应JSON解析失败: Expecting value: line 2 column 1 (char 1)
2025-05-29 16:08:47,894 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:748 - _ai_generate_fix_plan - 文本解析也失败，使用智能fallback策略
2025-05-29 16:08:47,895 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:829 - _create_intelligent_fallback_plan_async - 🧠 创建智能fallback修复计划...
2025-05-29 16:08:47,898 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:519 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 16:08:47,898 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:524 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 16:08:47,898 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\aider-git-repos\ai-proxy
2025-05-29 16:08:51,584 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 16:08:51,585 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 16:08:54,118 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 16:08:54,118 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 16:08:54,118 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 16:08:54,119 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 1): OPENROUTER_API_KEY环境变量未设置
2025-05-29 16:08:54,119 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 1.2 秒后重试...
2025-05-29 16:08:55,296 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 2): OPENROUTER_API_KEY环境变量未设置
2025-05-29 16:08:55,297 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 2.5 秒后重试...
2025-05-29 16:08:57,782 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 3): OPENROUTER_API_KEY环境变量未设置
2025-05-29 16:08:57,783 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 4.7 秒后重试...
2025-05-29 16:09:02,523 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:160 - execute_with_retry - 达到最大重试次数，使用降级策略
2025-05-29 16:09:02,524 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:741 - _ai_generate_fix_plan - AI响应JSON解析失败: Expecting value: line 2 column 1 (char 1)
2025-05-29 16:09:02,524 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:748 - _ai_generate_fix_plan - 文本解析也失败，使用智能fallback策略
2025-05-29 16:09:02,525 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:829 - _create_intelligent_fallback_plan_async - 🧠 创建智能fallback修复计划...
2025-05-29 16:10:49,462 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-29 16:10:49,462 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-29 16:10:49,462 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-29 16:10:49,464 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-29 16:10:49,464 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-29 16:10:49,721 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://***************/api/v4/version
2025-05-29 16:10:49,722 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-29 16:10:49,723 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-29 16:10:49,828 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:519 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 16:10:49,830 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:524 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 16:10:49,831 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\aider-git-repos\ai-proxy
2025-05-29 16:10:53,566 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 16:10:53,566 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 16:10:56,095 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 16:10:56,096 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 16:10:56,096 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 16:10:56,099 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:73 - __init__ - LLM重试处理器初始化完成
2025-05-29 16:10:56,253 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 1): OPENROUTER_API_KEY环境变量未设置
2025-05-29 16:10:56,253 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 1.2 秒后重试...
2025-05-29 16:10:57,488 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 2): OPENROUTER_API_KEY环境变量未设置
2025-05-29 16:10:57,489 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 2.3 秒后重试...
2025-05-29 16:10:59,790 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 3): OPENROUTER_API_KEY环境变量未设置
2025-05-29 16:10:59,791 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 5.0 秒后重试...
2025-05-29 16:11:04,798 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:160 - execute_with_retry - 达到最大重试次数，使用降级策略
2025-05-29 16:11:04,798 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:743 - _ai_generate_fix_plan - AI响应JSON解析失败: Expecting value: line 2 column 1 (char 1)
2025-05-29 16:11:04,798 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:750 - _ai_generate_fix_plan - 文本解析也失败，使用智能fallback策略
2025-05-29 16:11:04,799 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:831 - _create_intelligent_fallback_plan_async - 🧠 创建智能fallback修复计划...
2025-05-29 16:11:04,801 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:519 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-29 16:11:04,801 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:524 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-29 16:11:04,801 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\aider-git-repos\ai-proxy
2025-05-29 16:11:08,557 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-29 16:11:08,558 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
Name                                        NumberOfCores NumberOfLogicalProcessors
----                                        ------------- -------------------------
AMD Ryzen 7 7840H with Radeon 780M Graphics             8                        16


...
2025-05-29 16:11:11,032 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-29 16:11:11,032 - bot_agent - DEBUG - logging_config.py:141 - log_command_execution - 命令输出: 
TotalPhysicalMemory
-------------------
        16312553472


...
2025-05-29 16:11:11,032 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-29 16:11:11,033 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 1): OPENROUTER_API_KEY环境变量未设置
2025-05-29 16:11:11,034 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 1.1 秒后重试...
2025-05-29 16:11:12,161 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 2): OPENROUTER_API_KEY环境变量未设置
2025-05-29 16:11:12,162 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 2.3 秒后重试...
2025-05-29 16:11:14,428 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 3): OPENROUTER_API_KEY环境变量未设置
2025-05-29 16:11:14,429 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 4.9 秒后重试...
2025-05-29 16:11:19,292 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:160 - execute_with_retry - 达到最大重试次数，使用降级策略
2025-05-29 16:11:19,292 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:743 - _ai_generate_fix_plan - AI响应JSON解析失败: Expecting value: line 2 column 1 (char 1)
2025-05-29 16:11:19,293 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:750 - _ai_generate_fix_plan - 文本解析也失败，使用智能fallback策略
2025-05-29 16:11:19,293 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:831 - _create_intelligent_fallback_plan_async - 🧠 创建智能fallback修复计划...
