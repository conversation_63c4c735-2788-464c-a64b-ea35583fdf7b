"""
终端工具 - 执行shell命令和系统操作
"""

import os
import subprocess
import asyncio
import shlex
import platform
import time
from typing import Dict, List, Optional, Union
from pathlib import Path

from .base_tool import BaseTool, ToolResult


class TerminalTools(BaseTool):
    """
    终端工具

    功能：
    1. 执行shell命令
    2. 管理进程
    3. 环境变量操作
    4. 文件系统操作
    5. 系统信息获取
    """

    def __init__(self):
        super().__init__()
        self.is_windows = platform.system() == 'Windows'
        self.shell = 'powershell' if self.is_windows else '/bin/bash'

    def get_capabilities(self) -> List[str]:
        """获取工具能力"""
        return [
            'execute_command',
            'run_script',
            'check_process',
            'kill_process',
            'get_system_info',
            'manage_environment',
            'file_operations',
            'network_operations'
        ]

    def get_description(self) -> str:
        """获取工具描述"""
        return "终端工具 - 执行shell命令、管理进程、系统操作"

    async def execute_command(
        self,
        command: str,
        cwd: Optional[str] = None,
        timeout: int = 30,
        capture_output: bool = True,
        shell: bool = True,
        env: Optional[Dict[str, str]] = None
    ) -> ToolResult:
        """
        执行shell命令

        Args:
            command: 要执行的命令
            cwd: 工作目录
            timeout: 超时时间（秒）
            capture_output: 是否捕获输出
            shell: 是否使用shell
            env: 环境变量

        Returns:
            ToolResult: 执行结果
        """
        try:
            self.log_operation("execute_command", {"command": command, "cwd": cwd})

            # 准备环境变量
            exec_env = os.environ.copy()
            if env:
                exec_env.update(env)

            # 在Windows上使用PowerShell，正确处理引号转义
            if self.is_windows and shell:
                # 转义PowerShell中的双引号
                escaped_command = command.replace('"', '""')
                command = f'powershell -Command "{escaped_command}"'

            # 执行命令
            start_time = time.time()

            # 在Windows下使用同步方式避免asyncio问题
            if self.is_windows:
                try:
                    result = subprocess.run(
                        command,
                        cwd=cwd,
                        capture_output=capture_output,
                        text=True,
                        timeout=timeout,
                        env=exec_env,
                        shell=True
                    )

                    execution_time = time.time() - start_time
                    stdout_text = result.stdout if result.stdout else ""
                    stderr_text = result.stderr if result.stderr else ""
                    success = result.returncode == 0
                    return_code = result.returncode

                except subprocess.TimeoutExpired:
                    return ToolResult(
                        success=False,
                        error=f"命令执行超时 ({timeout}秒)",
                        message=f"命令 '{command}' 执行超时"
                    )
            else:
                # Linux/Mac使用异步方式
                process = await asyncio.create_subprocess_shell(
                    command,
                    cwd=cwd,
                    stdout=subprocess.PIPE if capture_output else None,
                    stderr=subprocess.PIPE if capture_output else None,
                    env=exec_env
                )

                try:
                    stdout, stderr = await asyncio.wait_for(
                        process.communicate(),
                        timeout=timeout
                    )
                except asyncio.TimeoutError:
                    process.kill()
                    await process.wait()
                    return ToolResult(
                        success=False,
                        error=f"命令执行超时 ({timeout}秒)",
                        message=f"命令 '{command}' 执行超时"
                    )

                execution_time = time.time() - start_time

                # 解码输出
                stdout_text = stdout.decode('utf-8', errors='replace') if stdout else ""
                stderr_text = stderr.decode('utf-8', errors='replace') if stderr else ""
                success = process.returncode == 0
                return_code = process.returncode

            # 使用增强的日志记录
            from bot_agent.utils.logging_config import global_enhanced_logger
            global_enhanced_logger.log_command_execution(
                command, success,
                output=stdout_text,
                error=stderr_text
            )

            return ToolResult(
                success=success,
                data={
                    'stdout': stdout_text,
                    'stderr': stderr_text,
                    'return_code': return_code,
                    'command': command,
                    'cwd': cwd
                },
                message=f"命令执行{'成功' if success else '失败'}",
                execution_time=execution_time
            )

        except Exception as e:
            # 使用增强的日志记录
            from bot_agent.utils.logging_config import global_enhanced_logger
            global_enhanced_logger.log_exception(f"执行命令失败: {command}")
            global_enhanced_logger.log_command_execution(command, False, error=str(e))

            self.log_error(e, f"执行命令: {command}")
            return ToolResult(
                success=False,
                error=str(e),
                message=f"命令执行异常: {command}"
            )

    async def run_script(
        self,
        script_content: str,
        script_type: str = 'python',
        cwd: Optional[str] = None,
        timeout: int = 60
    ) -> ToolResult:
        """
        运行脚本

        Args:
            script_content: 脚本内容
            script_type: 脚本类型 (python, bash, powershell)
            cwd: 工作目录
            timeout: 超时时间

        Returns:
            ToolResult: 执行结果
        """
        try:
            # 创建临时脚本文件
            import tempfile

            if script_type == 'python':
                suffix = '.py'
                interpreter = 'python'
            elif script_type == 'bash':
                suffix = '.sh'
                interpreter = 'bash'
            elif script_type == 'powershell':
                suffix = '.ps1'
                interpreter = 'powershell -ExecutionPolicy Bypass -File'
            else:
                return ToolResult(
                    success=False,
                    error=f"不支持的脚本类型: {script_type}"
                )

            with tempfile.NamedTemporaryFile(
                mode='w',
                suffix=suffix,
                delete=False,
                encoding='utf-8'
            ) as f:
                f.write(script_content)
                script_path = f.name

            try:
                # 执行脚本
                command = f"{interpreter} {script_path}"
                result = await self.execute_command(
                    command,
                    cwd=cwd,
                    timeout=timeout
                )

                # 添加脚本信息
                if result.data:
                    result.data['script_type'] = script_type
                    result.data['script_content'] = script_content[:500] + "..." if len(script_content) > 500 else script_content

                return result

            finally:
                # 清理临时文件
                try:
                    os.unlink(script_path)
                except:
                    pass

        except Exception as e:
            self.log_error(e, f"运行脚本: {script_type}")
            return ToolResult(
                success=False,
                error=str(e),
                message=f"脚本执行异常: {script_type}"
            )

    async def check_process(self, process_name: str) -> ToolResult:
        """
        检查进程是否运行

        Args:
            process_name: 进程名称

        Returns:
            ToolResult: 检查结果
        """
        try:
            if self.is_windows:
                command = f'Get-Process -Name "{process_name}" -ErrorAction SilentlyContinue'
            else:
                command = f'pgrep -f "{process_name}"'

            result = await self.execute_command(command)

            if result.success and result.data['stdout'].strip():
                return ToolResult(
                    success=True,
                    data={
                        'running': True,
                        'process_name': process_name,
                        'details': result.data['stdout']
                    },
                    message=f"进程 {process_name} 正在运行"
                )
            else:
                return ToolResult(
                    success=True,
                    data={
                        'running': False,
                        'process_name': process_name
                    },
                    message=f"进程 {process_name} 未运行"
                )

        except Exception as e:
            self.log_error(e, f"检查进程: {process_name}")
            return ToolResult(
                success=False,
                error=str(e),
                message=f"检查进程失败: {process_name}"
            )

    async def kill_process(self, process_name: str, force: bool = False) -> ToolResult:
        """
        终止进程

        Args:
            process_name: 进程名称
            force: 是否强制终止

        Returns:
            ToolResult: 操作结果
        """
        try:
            if self.is_windows:
                flag = '/F' if force else ''
                command = f'taskkill {flag} /IM "{process_name}"'
            else:
                signal = '-9' if force else '-15'
                command = f'pkill {signal} -f "{process_name}"'

            result = await self.execute_command(command)

            return ToolResult(
                success=result.success,
                data={
                    'process_name': process_name,
                    'force': force,
                    'output': result.data['stdout'] if result.data else None
                },
                message=f"进程 {process_name} {'强制' if force else ''}终止{'成功' if result.success else '失败'}"
            )

        except Exception as e:
            self.log_error(e, f"终止进程: {process_name}")
            return ToolResult(
                success=False,
                error=str(e),
                message=f"终止进程失败: {process_name}"
            )

    async def get_system_info(self) -> ToolResult:
        """
        获取系统信息

        Returns:
            ToolResult: 系统信息
        """
        try:
            info = {
                'platform': platform.platform(),
                'system': platform.system(),
                'release': platform.release(),
                'version': platform.version(),
                'machine': platform.machine(),
                'processor': platform.processor(),
                'python_version': platform.python_version(),
            }

            # 获取CPU和内存信息
            if self.is_windows:
                cpu_cmd = 'Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors'
                memory_cmd = 'Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory'
            else:
                cpu_cmd = 'cat /proc/cpuinfo | grep "model name" | head -1'
                memory_cmd = 'cat /proc/meminfo | grep MemTotal'

            cpu_result = await self.execute_command(cpu_cmd)
            memory_result = await self.execute_command(memory_cmd)

            if cpu_result.success:
                info['cpu_info'] = cpu_result.data['stdout'].strip()

            if memory_result.success:
                info['memory_info'] = memory_result.data['stdout'].strip()

            return ToolResult(
                success=True,
                data=info,
                message="系统信息获取成功"
            )

        except Exception as e:
            self.log_error(e, "获取系统信息")
            return ToolResult(
                success=False,
                error=str(e),
                message="获取系统信息失败"
            )

    async def manage_environment(
        self,
        action: str,
        var_name: str,
        var_value: Optional[str] = None
    ) -> ToolResult:
        """
        管理环境变量

        Args:
            action: 操作类型 (get, set, unset, list)
            var_name: 变量名
            var_value: 变量值（set操作时需要）

        Returns:
            ToolResult: 操作结果
        """
        try:
            if action == 'get':
                value = os.environ.get(var_name)
                return ToolResult(
                    success=True,
                    data={'name': var_name, 'value': value},
                    message=f"环境变量 {var_name} = {value}"
                )

            elif action == 'set':
                if var_value is None:
                    return ToolResult(
                        success=False,
                        error="设置环境变量需要提供值"
                    )

                os.environ[var_name] = var_value
                return ToolResult(
                    success=True,
                    data={'name': var_name, 'value': var_value},
                    message=f"环境变量 {var_name} 设置成功"
                )

            elif action == 'unset':
                if var_name in os.environ:
                    del os.environ[var_name]
                    return ToolResult(
                        success=True,
                        message=f"环境变量 {var_name} 删除成功"
                    )
                else:
                    return ToolResult(
                        success=False,
                        error=f"环境变量 {var_name} 不存在"
                    )

            elif action == 'list':
                env_vars = dict(os.environ)
                if var_name:  # 过滤包含特定名称的变量
                    env_vars = {k: v for k, v in env_vars.items() if var_name.lower() in k.lower()}

                return ToolResult(
                    success=True,
                    data=env_vars,
                    message=f"环境变量列表 (共 {len(env_vars)} 个)"
                )

            else:
                return ToolResult(
                    success=False,
                    error=f"不支持的操作: {action}"
                )

        except Exception as e:
            self.log_error(e, f"环境变量操作: {action}")
            return ToolResult(
                success=False,
                error=str(e),
                message=f"环境变量操作失败: {action}"
            )

    async def execute(self, *args, **kwargs) -> ToolResult:
        """执行工具操作"""
        # 默认执行命令操作
        if args:
            return await self.execute_command(args[0], **kwargs)
        else:
            return ToolResult(
                success=False,
                error="缺少命令参数"
            )
