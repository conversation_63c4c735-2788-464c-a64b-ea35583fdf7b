<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Aider行为分析系统{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- ApexCharts for better charts -->
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <!-- 自定义样式 -->
    <link href="/static/css/custom.css" rel="stylesheet">

    <style>
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .sidebar {
            min-height: 100vh;
            background: linear-gradient(180deg, #1e3c72 0%, #2a5298 100%);
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.85);
            border-radius: 12px;
            margin: 4px 8px;
            padding: 12px 16px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            font-weight: 500;
        }

        .sidebar .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }

        .sidebar .nav-link:hover::before {
            left: 100%;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.15);
            transform: translateX(8px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .sidebar .nav-link i {
            width: 20px;
            margin-right: 12px;
        }

        .main-content {
            background: transparent;
            min-height: 100vh;
            padding: 0;
        }

        .content-wrapper {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px 0 0 0;
            min-height: 100vh;
            box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
        }

        .card {
            border: none;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
        }

        .card:hover {
            transform: translateY(-8px);
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
        }

        .status-badge {
            font-size: 0.75em;
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .status-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .status-failed {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }

        .status-progress {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .conversation-round {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 16px;
            border-left: 4px solid #3b82f6;
            padding: 24px;
            margin: 20px 0;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .conversation-round:hover {
            transform: translateX(8px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }

        .conversation-round.failed {
            border-left-color: #ef4444;
            background: rgba(254, 242, 242, 0.8);
        }

        .conversation-round.success {
            border-left-color: #10b981;
            background: rgba(240, 253, 244, 0.8);
        }

        .code-block {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: #e2e8f0;
            border: 1px solid #475569;
            border-radius: 12px;
            padding: 20px;
            font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.6;
            max-height: 400px;
            overflow-y: auto;
            position: relative;
        }

        .code-block::-webkit-scrollbar {
            width: 8px;
        }

        .code-block::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 4px;
        }

        .code-block::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
        }

        .metric-card {
            text-align: center;
            padding: 32px 24px;
            border-radius: 20px;
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            transform: rotate(45deg);
        }

        .metric-value {
            font-size: 3em;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .metric-label {
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.95em;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .loading {
            text-align: center;
            padding: 40px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .search-filters {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .session-item {
            cursor: pointer;
            transition: all 0.3s;
        }

        .session-item:hover {
            background: #f8f9fa;
            border-radius: 8px;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white fw-bold">
                            <i class="fas fa-robot"></i>
                            Aider行为分析
                        </h4>
                        <p class="text-white-50 small mb-0">AI编程助手优化平台</p>
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/">
                                <i class="fas fa-chart-line"></i>
                                实时监控
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/sessions">
                                <i class="fas fa-history"></i>
                                行为记录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/statistics">
                                <i class="fas fa-chart-bar"></i>
                                性能分析
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/errors">
                                <i class="fas fa-exclamation-triangle"></i>
                                错误监控
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/execution-flow">
                                <i class="fas fa-project-diagram"></i>
                                执行链路
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/analysis-demo">
                                <i class="fas fa-link"></i>
                                分析链路演示
                            </a>
                        </li>
                    </ul>

                    <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">

                    <div class="text-white-50 small px-3">
                        <div class="mb-2">
                            <i class="fas fa-brain"></i>
                            AI行为分析
                        </div>
                        <div class="mb-2">
                            <i class="fas fa-chart-pie"></i>
                            性能优化
                        </div>
                        <div class="mb-2">
                            <i class="fas fa-shield-alt"></i>
                            错误预防
                        </div>
                        <div class="mb-2">
                            <i class="fas fa-rocket"></i>
                            效率提升
                        </div>
                    </div>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 main-content">
                <div class="content-wrapper">
                    <div class="p-4">
                        {% block content %}{% endblock %}
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- 通用JavaScript -->
    <script>
        // 格式化时间
        function formatTime(timestamp) {
            if (!timestamp) return '未知';
            const date = new Date(timestamp);
            return date.toLocaleString('zh-CN');
        }

        // 格式化持续时间
        function formatDuration(seconds) {
            if (!seconds) return '0秒';
            if (seconds < 60) return `${seconds.toFixed(1)}秒`;
            if (seconds < 3600) return `${(seconds / 60).toFixed(1)}分钟`;
            return `${(seconds / 3600).toFixed(1)}小时`;
        }

        // 获取状态徽章HTML
        function getStatusBadge(status) {
            const badges = {
                'success': '<span class="status-badge status-success"><i class="fas fa-check"></i> 成功</span>',
                'failed': '<span class="status-badge status-failed"><i class="fas fa-times"></i> 失败</span>',
                'in_progress': '<span class="status-badge status-progress"><i class="fas fa-spinner fa-spin"></i> 进行中</span>',
                'started': '<span class="status-badge status-progress"><i class="fas fa-play"></i> 已开始</span>'
            };
            return badges[status] || `<span class="status-badge">${status}</span>`;
        }

        // 显示加载状态
        function showLoading(containerId) {
            document.getElementById(containerId).innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <div>加载中...</div>
                </div>
            `;
        }

        // 显示错误信息
        function showError(containerId, message) {
            document.getElementById(containerId).innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    ${message}
                </div>
            `;
        }

        // 高亮当前页面的导航链接
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar .nav-link');

            navLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
